ENV=development

DATABASE_URL="postgresql://postgres:<EMAIL>:39088/railway?schema=public"
# DATABASE_URL="postgresql://postgres:<EMAIL>:38684/railway?schema=public"
SOCKET_IO_PATH=/api/socket/io

JWT_SECRET="secret"
JWT_EXPIRES_IN="24h"

# AWS S3 Configuration
AWS_ACCESS_KEY_ID=********************
AWS_SECRET_ACCESS_KEY=PEczrQY04ytO7H92/sGEQjaAbI7Q+xxY6iNnbIST
AWS_REGION=ap-southeast-1
AWS_S3_BUCKET_NAME=hiasangma

NEXT_PUBLIC_AWS_CDN_URL=https://d1y2ljh841nvy9.cloudfront.net

NEXT_PUBLIC_LITE_LLM_API_KEY=sk-1E8AEDgVP0qWoHXIP2VBAQ
NEXT_PUBLIC_LITE_LLM_ENDPOINT=https://llm-dev.lucablock.io
#NEXT_PUBLIC_LITE_LLM_API_KEY=************************************************************************************
#NEXT_PUBLIC_LITE_LLM_ENDPOINT=https://api.x.ai

NEXT_PUBLIC_SOCKET_URL=http://localhost:8081
#NEXT_PUBLIC_SOCKET_URL=https://hia-sang-ma-socket-production.up.railway.app
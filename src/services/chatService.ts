import { getCookie } from 'cookies-next';

const API_BASE = '/api/v1';

// Helper to get auth token
const getAuthToken = () => {
  return getCookie('access_token');
};

// Helper to make authenticated requests
const apiRequest = async (endpoint: string, options: RequestInit = {}) => {
  const token = getAuthToken();

  const response = await fetch(`${API_BASE}${endpoint}`, {
    ...options,
    headers: {
      'Content-Type': 'application/json',
      ...(token && { Authorization: `Bearer ${token}` }),
      ...options.headers,
    },
  });

  if (!response.ok) {
    const error = await response.json().catch(() => ({ error: 'Network error' }));
    throw new Error(error.error || `HTTP ${response.status}`);
  }

  return response.json();
};

// Chat API functions
export const chatApi = {
  // Get user's chats
  getChats: async (filters?: {
    chatType?: string;
    userId?: number;
    organizationId?: number;
    departmentId?: number;
    taskId?: number;
  }) => {
    const params = new URLSearchParams();
    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined) {
          params.append(key, value.toString());
        }
      });
    }
    const queryString = params.toString();
    return apiRequest(`/chat${queryString ? `?${queryString}` : ''}`);
  },

  // Get single chat
  getChat: async (chatId: number) => {
    return apiRequest(`/chat?id=${chatId}`);
  },

  // Create new chat
  createChat: async (data: {
    name?: string;
    chatType: 'private' | 'task' | 'department' | 'organization';
    organizationId?: number;
    departmentId?: number;
    taskId?: number;
    participantIds?: number[];
  }) => {
    return apiRequest('/chat', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  },

  // Update chat
  updateChat: async (id: number, data: { name?: string; isActive?: boolean; isBot?: boolean; botDuration?: number }) => {
    return apiRequest('/chat', {
      method: 'PATCH',
      body: JSON.stringify({ id, ...data }),
    });
  },

  // Delete chat
  deleteChat: async (chatId: number) => {
    return apiRequest(`/chat?id=${chatId}`, {
      method: 'DELETE',
    });
  },

  // Check if chat already exists
  checkChatExists: async (filters: {
    chatType: string;
    organizationId?: number;
    departmentId?: number;
    taskId?: number;
    participantIds?: number[];
  }) => {
    const chats = await chatApi.getChats(filters);

    if (filters.chatType === 'private' && filters.participantIds) {
      // For private chats, check if a chat with exact participants exists
      return chats.chats?.find((chat: any) => {
        const chatUserIds = chat.chatUsers.map((cu: any) => cu.userId).sort();
        const targetUserIds = filters.participantIds!.sort();
        return (
          chatUserIds.length === targetUserIds.length &&
          chatUserIds.every((id: number, index: number) => id === targetUserIds[index])
        );
      });
    }

    // For other types, check by specific entity
    return chats.chats?.find(
      (chat: any) =>
        chat.chatType.toLowerCase() === filters.chatType.toLowerCase() &&
        (filters.organizationId ? chat.organizationId === filters.organizationId : true) &&
        (filters.departmentId ? chat.departmentId === filters.departmentId : true) &&
        (filters.taskId ? chat.taskId === filters.taskId : true)
    );
  },
};

// Chat User API functions
export const chatUserApi = {
  // Get chat users
  getChatUsers: async (chatId: number) => {
    return apiRequest(`/chat-user?chatId=${chatId}`);
  },

  // Get user's chats
  getUserChats: async (userId: number) => {
    return apiRequest(`/chat-user?userId=${userId}`);
  },

  // Add user to chat
  addUserToChat: async (data: { chatId: number; userId: number; isAdmin?: boolean }) => {
    return apiRequest('/chat-user', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  },

  // Update chat user
  updateChatUser: async (id: number, data: { isAdmin?: boolean }) => {
    return apiRequest('/chat-user', {
      method: 'PATCH',
      body: JSON.stringify({ id, ...data }),
    });
  },

  // Remove user from chat
  removeChatUser: async (chatId: number, userId: number) => {
    return apiRequest(`/chat-user?chatId=${chatId}&userId=${userId}`, {
      method: 'DELETE',
    });
  },
};

// Chat Message API functions
export const chatMessageApi = {
  // Get messages
  getMessages: async (
    chatId: number,
    options?: {
      page?: number;
      limit?: number;
      before?: number;
      after?: number;
      includeReadStatus?: boolean;
    }
  ) => {
    const params = new URLSearchParams({ chatId: chatId.toString() });
    if (options) {
      Object.entries(options).forEach(([key, value]) => {
        if (value !== undefined) {
          params.append(key, value.toString());
        }
      });
    }
    return apiRequest(`/chat-message?${params.toString()}`);
  },

  // Send message
  sendMessage: async (data: {
    chatId: number;
    content: string;
    messageType?: 'TEXT' | 'IMAGE' | 'FILE' | 'STICKER';
  }) => {
    return apiRequest('/chat-message', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  },

  // Update message status
  updateMessageStatus: async (id: number, messageStatus: 'DELIVERED' | 'READ' | 'FAILED') => {
    return apiRequest('/chat-message', {
      method: 'PATCH',
      body: JSON.stringify({ id, messageStatus }),
    });
  },

  // Delete message
  deleteMessage: async (messageId: number) => {
    return apiRequest(`/chat-message?id=${messageId}`, {
      method: 'DELETE',
    });
  },

  // Mark message as read
  markMessageAsRead: async (messageId: number) => {
    return apiRequest('/chat-message/mark-read', {
      method: 'POST',
      body: JSON.stringify({ messageId }),
    });
  },

  // Mark all messages in chat as read
  markChatAsRead: async (chatId: number, beforeMessageId?: number) => {
    const body: any = { chatId };
    if (beforeMessageId) {
      body.beforeMessageId = beforeMessageId;
    }
    return apiRequest('/chat-message/mark-read', {
      method: 'POST',
      body: JSON.stringify(body),
    });
  },

  // Mark message as unread
  markMessageAsUnread: async (messageId: number) => {
    return apiRequest('/chat-message/mark-read', {
      method: 'DELETE',
      body: JSON.stringify({ messageId }),
    });
  },

  // Get unread count for specific chat
  getUnreadCount: async (chatId: number) => {
    return apiRequest(`/chat-message/unread-count?chatId=${chatId}`);
  },

  // Get unread counts for all chats
  getAllUnreadCounts: async () => {
    return apiRequest('/chat-message/unread-count');
  },

  // Get total unread count
  getTotalUnreadCount: async () => {
    return apiRequest('/chat-message/unread-count?total=true');
  },

  // Get detailed read status for a single message
  getMessageReadStatus: async (messageId: number) => {
    return apiRequest(`/chat-message/read-status?messageId=${messageId}`);
  },

  // Get detailed read status for multiple messages
  getMultipleMessageReadStatus: async (messageIds: number[]) => {
    return apiRequest(`/chat-message/read-status?messageIds=${messageIds.join(',')}`);
  },

  // Get read status for all messages in a chat
  getChatReadStatus: async (chatId: number, options?: { page?: number; limit?: number }) => {
    const params = new URLSearchParams({ chatId: chatId.toString() });
    if (options) {
      Object.entries(options).forEach(([key, value]) => {
        if (value !== undefined) {
          params.append(key, value.toString());
        }
      });
    }
    return apiRequest(`/chat-message/read-status?${params.toString()}`);
  },
};

// User and organization API functions for modal data
export const dataApi = {
  // Get current user info
  getCurrentUser: async () => {
    return apiRequest('/me');
  },

  // Get organization members
  getOrganizationMembers: async (organizationId: number) => {
    return apiRequest(`/member?organizationId=${organizationId}`);
  },

  // Get department members
  getDepartmentMembers: async (departmentId: number) => {
    return apiRequest(`/member?departmentId=${departmentId}`);
  },

  // Get user's departments
  getUserDepartments: async (userId: number) => {
    return apiRequest(`/department?userId=${userId}`);
  },

  // Get departments by organization
  getOrganizationDepartments: async (organizationId: number) => {
    return apiRequest(`/department?organizationId=${organizationId}`);
  },

  // Get user's organizations
  getUserOrganizations: async (userId: number) => {
    return apiRequest(`/organization?userId=${userId}`);
  },

  // Get task details
  getTask: async (taskId: number) => {
    return apiRequest(`/task?id=${taskId}`);
  },

  // Get user's tasks (assigned to or created by user)
  getUserTasks: async (userId: number) => {
    return apiRequest(`/task?assignedToUserId=${userId}`);
  },

  // Get all tasks the user can access (for task chat selection)
  getAccessibleTasks: async () => {
    return apiRequest('/task');
  },

  // Role-based API methods for chat modal
  // Get organizations based on user role
  getChatModalOrganizations: async () => {
    return apiRequest('/chat-modal/organizations');
  },

  // Get departments based on organization and user role
  getChatModalDepartments: async (organizationId: number) => {
    return apiRequest(`/chat-modal/departments?organizationId=${organizationId}`);
  },

  // Get users based on organization, department, and user role
  getChatModalUsers: async (organizationId: number, departmentId?: number) => {
    const params = new URLSearchParams({ organizationId: organizationId.toString() });
    if (departmentId) {
      params.append('departmentId', departmentId.toString());
    }
    return apiRequest(`/chat-modal/users?${params.toString()}`);
  },
};

// Assistant API functions
export const assistantApi = {
  // Get assistant message types
  getMessageTypes: async () => {
    return apiRequest('/assistant-message-type');
  },

  // Get assistant chat users
  getAssistantChatUsers: async (filters?: {
    userId?: number;
    chatId?: number;
    isActive?: boolean;
  }) => {
    const params = new URLSearchParams();
    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined) {
          params.append(key, value.toString());
        }
      });
    }
    const queryString = params.toString();
    return apiRequest(`/assistant-chat-user${queryString ? `?${queryString}` : ''}`);
  },

  // Get single assistant chat user
  getAssistantChatUser: async (id: number) => {
    return apiRequest(`/assistant-chat-user?id=${id}`);
  },

  // Create assistant chat user
  createAssistantChatUser: async (data: {
    name: string;
    chatId: number;
    isActive?: boolean;
  }) => {
    return apiRequest('/assistant-chat-user', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  },

  // Delete assistant chat user
  deleteAssistantChatUser: async (id: number) => {
    return apiRequest('/assistant-chat-user', {
      method: 'DELETE',
      body: JSON.stringify({ id }),
    });
  },

  // Get assistant messages
  getAssistantMessages: async (
    assistantChatUserId: number,
    options?: {
      page?: number;
      limit?: number;
      messageTypeId?: number;
      order?: 'asc' | 'desc';
    }
  ) => {
    const params = new URLSearchParams({
      assistantChatUserId: assistantChatUserId.toString()
    });
    if (options) {
      Object.entries(options).forEach(([key, value]) => {
        if (value !== undefined) {
          params.append(key, value.toString());
        }
      });
    }
    return apiRequest(`/assistant-message?${params.toString()}`);
  },

  // Create assistant message
  createAssistantMessage: async (data: {
    assistantChatUserId: number;
    assistantMessageTypeId: number;
    content: string;
    metadata?: any;
    completionTokens?: number;
    promptTokens?: number;
    totalTokens?: number;
  }) => {
    return apiRequest('/assistant-message', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  },
};

'use client';

import React, { useState } from 'react';
import styled from 'styled-components';
import { MessageSquare, Plus, RefreshCw } from 'lucide-react';
import { appTheme } from '@/app/theme';
import FeedbackTabs from './FeedbackTabs';
import FeedbackList from './FeedbackList';
import FeedbackDetail from './FeedbackDetail';
import CreateFeedbackModal from './CreateFeedbackModal';
import FeedbackStats from './FeedbackStats';
import ShareFeedbackModal from './ShareFeedbackModal';
import { feedbackShareApi } from '@/services/feedbackService';

const LayoutContainer = styled.div`
  display: flex;
  height: 100%;
  width: 100%;
`;

const Sidebar = styled.div<{ $isOpen: boolean }>`
  width: 320px;
  background: ${appTheme.colors.background.light};
  border-right: 1px solid ${appTheme.colors.border};
  display: flex;
  flex-direction: column;
  transition: ${appTheme.transitions.default};

  @media (max-width: ${appTheme.breakpoints.md}) {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    z-index: 10;
    transform: translateX(${props => (props.$isOpen ? '0' : '-100%')});
    box-shadow: ${props => (props.$isOpen ? appTheme.shadows.lg : 'none')};
  }
`;

const SidebarHeader = styled.div`
  padding: ${appTheme.spacing.lg};
  border-bottom: 1px solid ${appTheme.colors.border};
  background: ${appTheme.colors.background.main};
`;

const SidebarTitle = styled.h1`
  font-size: ${appTheme.typography.fontSizes.xl};
  font-weight: ${appTheme.typography.fontWeights.semibold};
  color: ${appTheme.colors.text.primary};
  margin: 0 0 ${appTheme.spacing.md} 0;
  display: flex;
  align-items: center;
  gap: ${appTheme.spacing.md};
`;

const SidebarActions = styled.div`
  display: flex;
  gap: ${appTheme.spacing.sm};
  align-items: center;
`;

const ActionButton = styled.button<{ $variant?: 'primary' | 'secondary' }>`
  padding: ${appTheme.spacing.sm} ${appTheme.spacing.md};
  border-radius: ${appTheme.borderRadius.md};
  border: 1px solid
    ${props => (props.$variant === 'primary' ? appTheme.colors.primary : appTheme.colors.border)};
  background-color: ${props =>
    props.$variant === 'primary' ? appTheme.colors.primary : appTheme.colors.background.main};
  color: ${props => (props.$variant === 'primary' ? 'white' : appTheme.colors.text.secondary)};
  font-size: ${appTheme.typography.fontSizes.sm};
  font-weight: ${appTheme.typography.fontWeights.medium};
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: ${appTheme.spacing.xs};
  transition: ${appTheme.transitions.default};

  &:hover {
    background-color: ${props =>
      props.$variant === 'primary'
        ? appTheme.colors.primaryHover
        : appTheme.colors.background.lighter};
    transform: translateY(-1px);
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
  }
`;

const SidebarContent = styled.div`
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
`;

const MainContent = styled.div<{ $sidebarOpen: boolean }>`
  flex: 1;
  display: flex;
  flex-direction: column;
  background: ${appTheme.colors.background.main};
  transition: ${appTheme.transitions.default};

  @media (max-width: ${appTheme.breakpoints.md}) {
    margin-left: 0;
    ${props =>
      props.$sidebarOpen &&
      `
      filter: blur(2px);
      pointer-events: none;
    `}
  }
`;

const MobileOverlay = styled.div<{ $isVisible: boolean }>`
  display: none;

  @media (max-width: ${appTheme.breakpoints.md}) {
    display: ${props => (props.$isVisible ? 'block' : 'none')};
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 9;
  }
`;

const MobileMenuButton = styled.button`
  display: none;

  @media (max-width: ${appTheme.breakpoints.md}) {
    display: flex;
    position: absolute;
    top: ${appTheme.spacing.lg};
    left: ${appTheme.spacing.lg};
    z-index: 11;
    padding: ${appTheme.spacing.sm};
    background: ${appTheme.colors.background.main};
    border: 1px solid ${appTheme.colors.border};
    border-radius: ${appTheme.borderRadius.md};
    cursor: pointer;
    align-items: center;
    justify-content: center;
    box-shadow: ${appTheme.shadows.sm};
  }
`;

type FeedbackTab = 'received' | 'given' | 'shared';

interface FeedbackLayoutProps {
  feedbackTypes: any[];
  myFeedback: {
    created: any[];
    assigned: any[];
    shared: any[];
  };
  onRefresh: () => void;
  isSocketConnected: boolean;
}

export default function FeedbackLayout({
  feedbackTypes,
  myFeedback,
  onRefresh,
  isSocketConnected,
}: FeedbackLayoutProps) {
  const [activeTab, setActiveTab] = useState<FeedbackTab>('received');
  const [selectedFeedback, setSelectedFeedback] = useState<any>(null);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [isMobileSidebarOpen, setIsMobileSidebarOpen] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [showShareModal, setShowShareModal] = useState(false);
  const [feedbackToShare, setFeedbackToShare] = useState<any>(null);
  const [isSharing, setIsSharing] = useState(false);

  // Get current feedback list based on active tab
  const getCurrentFeedbackList = () => {
    switch (activeTab) {
      case 'received':
        return myFeedback.assigned || [];
      case 'given':
        return myFeedback.created || [];
      case 'shared':
        return myFeedback.shared || [];
      default:
        return [];
    }
  };

  const handleRefresh = async () => {
    setIsRefreshing(true);
    try {
      await onRefresh();
    } finally {
      setIsRefreshing(false);
    }
  };

  const handleFeedbackSelect = (feedback: any ,) => {
    setSelectedFeedback(feedback);
    setIsMobileSidebarOpen(false);
  };

  const handleCreateFeedback = () => {
    setShowCreateModal(true);
  };

  const handleFeedbackCreated = () => {
    setShowCreateModal(false);
    handleRefresh();
  };

  const handleMobileSidebarToggle = () => {
    setIsMobileSidebarOpen(!isMobileSidebarOpen);
  };

  const handleMobileOverlayClick = () => {
    setIsMobileSidebarOpen(false);
  };

  const handleTabChange = (tab: FeedbackTab) => {
    setActiveTab(tab);
    setSelectedFeedback(null); // Reset selected feedback when tab changes
  };

  const handleShareFeedback = (feedback: any) => {
    setFeedbackToShare(feedback);
    setShowShareModal(true);
  };

  const handleShareModalClose = () => {
    setShowShareModal(false);
    setFeedbackToShare(null);
  };

  const handleShareSubmit = async (shareData: {
    feedbackUserId: number;
    shareUserIds: number[];
  }) => {
    if (!feedbackToShare) return;

    setIsSharing(true);
    try {
      // Call the updated API with feedbackUserId and shareUserIds
      await feedbackShareApi.shareFeedback(
        shareData.feedbackUserId,
        shareData.shareUserIds
      );
      handleRefresh(); // Refresh to show updated data
    } catch (error) {
      console.error('Error sharing feedback:', error);
      throw error; // Let the modal handle the error display
    } finally {
      setIsSharing(false);
    }
  };

  return (
    <LayoutContainer>
      <MobileMenuButton onClick={handleMobileSidebarToggle}>
        <MessageSquare size={20} />
      </MobileMenuButton>

      <MobileOverlay $isVisible={isMobileSidebarOpen} onClick={handleMobileOverlayClick} />

      <Sidebar $isOpen={isMobileSidebarOpen}>
        <SidebarHeader>
          <SidebarTitle>
            <MessageSquare size={24} />
            Feedback
          </SidebarTitle>
          <SidebarActions>
            <ActionButton onClick={handleRefresh} disabled={isRefreshing} title="รีเฟรช Feedback">
              <RefreshCw size={16} className={isRefreshing ? 'animate-spin' : ''} />
            </ActionButton>
            <ActionButton
              $variant="primary"
              onClick={handleCreateFeedback}
              title="สร้าง Feedback"
            >
              <Plus size={16} />
              สร้าง
            </ActionButton>
          </SidebarActions>
        </SidebarHeader>

        <SidebarContent>
          <FeedbackStats myFeedback={myFeedback} />

          <FeedbackTabs
            activeTab={activeTab}
            onTabChange={handleTabChange}
            feedbackCounts={{
              received: myFeedback.assigned?.length || 0,
              given: myFeedback.created?.length || 0,
              shared: myFeedback.shared?.length || 0,
            }}
          />

          <FeedbackList
            feedbacks={getCurrentFeedbackList()}
            selectedFeedback={selectedFeedback}
            onFeedbackSelect={handleFeedbackSelect}
            activeTab={activeTab}
            loading={isRefreshing}
            onShareFeedback={handleShareFeedback}
          />
        </SidebarContent>
      </Sidebar>

      <MainContent $sidebarOpen={isMobileSidebarOpen}>
        <FeedbackDetail
          feedback={selectedFeedback}
          activeTab={activeTab}
          onRefresh={handleRefresh}
          onMobileSidebarOpen={handleMobileSidebarToggle}
        />
      </MainContent>

      {showCreateModal && (
        <CreateFeedbackModal
          isOpen={showCreateModal}
          onClose={() => setShowCreateModal(false)}
          onFeedbackCreated={handleFeedbackCreated}
          feedbackTypes={feedbackTypes}
        />
      )}

      {/* Share Feedback Modal */}
      {feedbackToShare && (
        <ShareFeedbackModal
          isOpen={showShareModal}
          onClose={handleShareModalClose}
          onShare={handleShareSubmit}
          feedback={feedbackToShare}
          loading={isSharing}
        />
      )}
    </LayoutContainer>
  );
}

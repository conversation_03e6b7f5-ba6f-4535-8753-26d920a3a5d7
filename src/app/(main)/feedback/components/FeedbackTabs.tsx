'use client';

import React from 'react';
import styled from 'styled-components';
import { Inbox, Send, Share2 } from 'lucide-react';
import { appTheme } from '@/app/theme';

const TabsContainer = styled.div`
  display: flex;
  flex-direction: column;
  border-bottom: 1px solid ${appTheme.colors.border};
  background: ${appTheme.colors.background.main};
`;

const TabsList = styled.div`
  display: flex;
  width: 100%;
`;

const TabButton = styled.button<{ $active: boolean }>`
  flex: 1;
  padding: ${appTheme.spacing.md} ${appTheme.spacing.sm};
  border: none;
  background: ${props => props.$active ? appTheme.colors.background.main : appTheme.colors.background.light};
  color: ${props => props.$active ? appTheme.colors.primary : appTheme.colors.text.secondary};
  font-size: ${appTheme.typography.fontSizes.sm};
  font-weight: ${props => props.$active ? appTheme.typography.fontWeights.semibold : appTheme.typography.fontWeights.medium};
  cursor: pointer;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: ${appTheme.spacing.xs};
  transition: ${appTheme.transitions.default};
  border-bottom: 2px solid ${props => props.$active ? appTheme.colors.primary : 'transparent'};
  position: relative;

  &:hover {
    background: ${props => props.$active ? appTheme.colors.background.main : appTheme.colors.background.lighter};
    color: ${props => props.$active ? appTheme.colors.primary : appTheme.colors.text.primary};
  }

  &:first-child {
    border-left: none;
  }

  &:not(:last-child) {
    border-right: 1px solid ${appTheme.colors.border};
  }
`;

const TabIcon = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
`;

const TabLabel = styled.span`
  font-size: ${appTheme.typography.fontSizes.xs};
  text-transform: uppercase;
  letter-spacing: 0.5px;
`;

const TabBadge = styled.span<{ $active: boolean }>`
  position: absolute;
  top: 6px;
  right: 8px;
  background: ${props => props.$active ? appTheme.colors.primary : appTheme.colors.text.tertiary};
  color: white;
  font-size: 10px;
  font-weight: ${appTheme.typography.fontWeights.semibold};
  padding: 2px 6px;
  border-radius: 10px;
  min-width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  line-height: 1;
`;

type FeedbackTab = 'received' | 'given' | 'shared';

interface FeedbackTabsProps {
  activeTab: FeedbackTab;
  onTabChange: (tab: FeedbackTab) => void;
  feedbackCounts: {
    received: number;
    given: number;
    shared: number;
  };
}

const tabConfig = {
  received: {
    icon: Inbox,
    label: 'ได้รับ',
    description: 'Feedback ที่คุณได้รับ',
  },
  given: {
    icon: Send,
    label: 'ส่ง',
    description: 'Feedback ที่คุณสร้าง',
  },
  shared: {
    icon: Share2,
    label: 'แชร์',
    description: 'Feedback ที่แชร์ให้คุณ',
  },
};

export default function FeedbackTabs({
  activeTab,
  onTabChange,
  feedbackCounts,
}: FeedbackTabsProps) {
  return (
    <TabsContainer>
      <TabsList>
        {Object.entries(tabConfig).map(([key, config]) => {
          const tabKey = key as FeedbackTab;
          const Icon = config.icon;
          const count = feedbackCounts[tabKey];
          const isActive = activeTab === tabKey;

          return (
            <TabButton
              key={tabKey}
              $active={isActive}
              onClick={() => onTabChange(tabKey)}
              title={config.description}
            >
              <TabIcon>
                <Icon size={18} />
              </TabIcon>
              <TabLabel>{config.label}</TabLabel>
              {count > 0 && (
                <TabBadge $active={isActive}>
                  {count > 99 ? '99+' : count}
                </TabBadge>
              )}
            </TabButton>
          );
        })}
      </TabsList>
    </TabsContainer>
  );
}

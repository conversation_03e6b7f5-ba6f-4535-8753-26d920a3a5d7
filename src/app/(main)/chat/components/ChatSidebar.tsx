'use client';

import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { MessageCircle, Users, Building2, CheckSquare, Search, Plus, X, Bot, Clock } from 'lucide-react';
import { appTheme } from '@/app/theme';
import { formatSimpleTimestamp } from '@/utils/dateUtils';
import { replaceMentionsWithHTML } from '@/types/mention';

type RoomType = 'private' | 'task' | 'department' | 'organization';

interface Room {
  id: string;
  name: string;
  type: RoomType;
  lastMessage?: string;
  lastMessageTime?: string;
  lastMessageType?: string;
  lastMessageStatus?: string;
  unreadCount?: number;
  isOnline?: boolean;
  avatar?: string;
  isBot?: boolean;
  botDuration?: number;
  chatUsers?: Array<{
    user: {
      id: number;
      firstName: string;
      lastName: string;
      imageUrl?: string;
    };
  }>;
}

interface ChatSidebarProps {
  rooms: Record<RoomType, Room[]>;
  activeTab: RoomType;
  selectedRoom: Room | null;
  searchQuery: string;
  loading?: boolean;
  currentUser?: {
    id: number;
    firstName: string;
    lastName: string;
  };
  onTabChange: (tab: RoomType) => void | Promise<void>;
  onRoomSelect: (room: Room) => void;
  onSearchChange: (query: string) => void;
  onCreateChat: (chatType: RoomType) => void;
  onRefreshChats?: () => void | Promise<void>;
  // Mobile props
  isMobileOpen?: boolean;
  onMobileClose?: () => void;
}

const SidebarContainer = styled.div`
  width: 320px;
  border-right: 1px solid ${appTheme.colors.border};
  display: flex;
  flex-direction: column;
  background: ${appTheme.colors.background.light};
  flex-shrink: 0;
  position: relative;
  z-index: 10;

  /* Desktop (1024px+) - maintain current layout */
  @media (min-width: ${appTheme.breakpoints.lg}) {
    width: 320px;
  }

  /* Tablet (768px - 1023px) - slightly smaller width */
  @media (max-width: ${appTheme.breakpoints.lg}) and (min-width: ${appTheme.breakpoints.md}) {
    width: 280px;
  }

  /* Mobile (< 768px) - overlay behavior */
  @media (max-width: ${appTheme.breakpoints.md}) {
    position: fixed;
    top: 0;
    left: 0;
    width: 280px;
    height: 100vh;
    z-index: 1000;
    background: ${appTheme.colors.background.main};
    border-right: 1px solid ${appTheme.colors.border};
    box-shadow: ${appTheme.shadows.lg};
    transform: translateX(-100%);
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    height: calc(100vh - 70px);
    overflow-y: auto;

    &.mobile-open {
      transform: translateX(0);
    }
  }

  /* Small mobile (< 480px) - full width overlay */
  @media (max-width: ${appTheme.breakpoints.sm}) {
    width: 100%;
    border-right: none;
  }
`;

const SidebarHeader = styled.div`
  padding: ${appTheme.spacing.lg};
  border-bottom: 1px solid ${appTheme.colors.border};
  display: flex;
  flex-direction: column;
  justify-content: center;
  height: 70px;
  position: relative;

  /* Tablet - slightly reduce padding */
  @media (max-width: ${appTheme.breakpoints.lg}) and (min-width: ${appTheme.breakpoints.md}) {
    padding: ${appTheme.spacing.md};
    height: 65px;
  }

  /* Mobile - add close button and adjust layout */
  @media (max-width: ${appTheme.breakpoints.md}) {
    padding: ${appTheme.spacing.md};
    height: 60px;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
  }

  /* Small mobile - reduce padding further */
  @media (max-width: ${appTheme.breakpoints.sm}) {
    padding: ${appTheme.spacing.sm} ${appTheme.spacing.md};
    height: 55px;
  }
`;

const HeaderRow = styled.div`
  display: flex;
  align-items: center;
  gap: ${appTheme.spacing.sm};
`;

const HeaderTitle = styled.h2`
  margin: 0;
  flex: 1;
  color: ${appTheme.colors.text.primary};
  font-size: 18px;

  /* Tablet - slightly smaller font */
  @media (max-width: ${appTheme.breakpoints.lg}) and (min-width: ${appTheme.breakpoints.md}) {
    font-size: 17px;
  }

  /* Mobile - smaller font and adjust for close button */
  @media (max-width: ${appTheme.breakpoints.md}) {
    font-size: 16px;
    flex: none;
  }

  /* Small mobile - even smaller font */
  @media (max-width: ${appTheme.breakpoints.sm}) {
    font-size: 15px;
  }
`;

const CreateButton = styled.button`
  display: flex;
  align-items: center;
  gap: ${appTheme.spacing.xs};
  padding: ${appTheme.spacing.xs} ${appTheme.spacing.sm};
  border: 1px solid ${appTheme.colors.primary};
  border-radius: ${appTheme.borderRadius.md};
  background: ${appTheme.colors.primary};
  color: ${appTheme.colors.background.main};
  cursor: pointer;
  font-size: 12px;
  transition: all 0.2s ease;
  white-space: nowrap;

  &:hover {
    background: ${appTheme.colors.primaryHover};
  }

  /* Mobile - touch-friendly sizing */
  @media (max-width: ${appTheme.breakpoints.md}) {
    padding: ${appTheme.spacing.sm};
    min-height: 32px;
    min-width: 32px;
    font-size: 16px;
    justify-content: center;
  }

  /* Small mobile - icon only */
  @media (max-width: ${appTheme.breakpoints.sm}) {
    padding: ${appTheme.spacing.sm};

    span {
      display: none;
    }
  }
`;

const SearchInput = styled.input`
  width: 100%;
  padding: ${appTheme.spacing.sm} ${appTheme.spacing.md};
  border: 1px solid ${appTheme.colors.border};
  border-radius: ${appTheme.borderRadius.md};
  font-size: 14px;
  outline: none;

  &:focus {
    border-color: ${appTheme.colors.primary};
  }
`;

// Mobile overlay and close button
const MobileOverlay = styled.div<{ $isVisible: boolean }>`
  display: none;

  @media (max-width: ${appTheme.breakpoints.md}) {
    display: ${props => (props.$isVisible ? 'block' : 'none')};
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 999;
    backdrop-filter: blur(2px);
  }
`;

const MobileCloseButton = styled.button`
  display: none;

  @media (max-width: ${appTheme.breakpoints.md}) {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border: none;
    background: ${appTheme.colors.background.lighter};
    border-radius: ${appTheme.borderRadius.sm};
    color: ${appTheme.colors.text.secondary};
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      background: ${appTheme.colors.border};
      color: ${appTheme.colors.text.primary};
    }

    &:active {
      transform: scale(0.95);
    }
  }
`;

const TabsContainer = styled.div`
  display: flex;
  border-bottom: 1px solid ${appTheme.colors.border};
  background: ${appTheme.colors.background.main};
  overflow-x: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;

  &::-webkit-scrollbar {
    display: none;
  }

  /* Mobile - allow horizontal scrolling if needed */
  @media (max-width: ${appTheme.breakpoints.md}) {
    padding: 0 ${appTheme.spacing.xs};
  }
`;

const Tab = styled.button<{ $isActive: boolean; $isLoading?: boolean }>`
  display: flex;
  align-items: center;
  justify-content: center;
  flex: 1;
  height: 48px;
  border: none;
  background: none;
  color: ${props => (props.$isActive ? appTheme.colors.primary : appTheme.colors.text.secondary)};
  cursor: pointer;
  border-bottom: 3px solid ${props => (props.$isActive ? appTheme.colors.primary : 'transparent')};
  transition: all 0.2s ease;
  position: relative;
  opacity: ${props => (props.$isLoading ? 0.7 : 1)};
  min-width: 60px;

  &:hover {
    color: ${appTheme.colors.primary};
    background: ${appTheme.colors.background.lighter};
  }

  &:disabled {
    cursor: not-allowed;
    opacity: 0.5;
  }

  /* Mobile - touch-friendly sizing */
  @media (max-width: ${appTheme.breakpoints.md}) {
    height: 52px;
    min-width: 70px;
    padding: ${appTheme.spacing.sm};
  }

  /* Small mobile - slightly smaller */
  @media (max-width: ${appTheme.breakpoints.sm}) {
    height: 48px;
    min-width: 65px;
  }
`;

const RoomList = styled.div`
  flex: 1;
  overflow-y: auto;
  padding: ${appTheme.spacing.sm};
  scrollbar-width: thin;
  scrollbar-color: ${appTheme.colors.border} transparent;

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background: ${appTheme.colors.border};
    border-radius: 3px;
  }

  /* Mobile - adjust padding and scrolling */
  @media (max-width: ${appTheme.breakpoints.md}) {
    padding: ${appTheme.spacing.xs} ${appTheme.spacing.sm};
    -webkit-overflow-scrolling: touch;
  }

  /* Small mobile - minimal padding */
  @media (max-width: ${appTheme.breakpoints.sm}) {
    padding: ${appTheme.spacing.xs};
  }
`;

const RoomItem = styled.div<{ $isActive: boolean }>`
  display: flex;
  align-items: center;
  gap: ${appTheme.spacing.md};
  padding: ${appTheme.spacing.md};
  margin-bottom: ${appTheme.spacing.xs};
  border-radius: ${appTheme.borderRadius.md};
  cursor: pointer;
  background: ${props => (props.$isActive ? appTheme.colors.primaryLight : 'transparent')};
  border: ${props =>
    props.$isActive ? `1px solid ${appTheme.colors.primary}` : '1px solid transparent'};
  transition: all 0.2s ease;
  min-height: 60px;

  &:hover {
    background: ${props =>
      props.$isActive ? appTheme.colors.primaryLight : appTheme.colors.background.lighter};
  }

  /* Tablet - slightly reduce padding */
  @media (max-width: ${appTheme.breakpoints.lg}) and (min-width: ${appTheme.breakpoints.md}) {
    padding: ${appTheme.spacing.sm} ${appTheme.spacing.md};
    gap: ${appTheme.spacing.sm};
  }

  /* Mobile - touch-friendly sizing */
  @media (max-width: ${appTheme.breakpoints.md}) {
    padding: ${appTheme.spacing.md};
    min-height: 64px;
    gap: ${appTheme.spacing.md};
    margin-bottom: ${appTheme.spacing.sm};

    &:active {
      transform: scale(0.98);
      background: ${props =>
        props.$isActive ? appTheme.colors.primaryLight : appTheme.colors.background.lighter};
    }
  }

  /* Small mobile - optimize spacing */
  @media (max-width: ${appTheme.breakpoints.sm}) {
    padding: ${appTheme.spacing.sm} ${appTheme.spacing.md};
    min-height: 60px;
    gap: ${appTheme.spacing.sm};
  }
`;

const RoomAvatar = styled.div<{ $type: RoomType }>`
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  background: ${props => {
    switch (props.$type) {
      case 'private':
        return appTheme.colors.primary;
      case 'task':
        return appTheme.colors.secondary;
      case 'department':
        return '#10b981';
      case 'organization':
        return '#f59e0b';
      default:
        return appTheme.colors.primary;
    }
  }};
  color: white;
  font-size: 14px;
  font-weight: 500;
  overflow: hidden;
`;

const ProfileImage = styled.img`
  width: 100%;
  height: 100%;
  border-radius: 50%;
  object-fit: cover;
`;

const ProfileInitials = styled.div`
  font-size: 14px;
  font-weight: 600;
  color: white;
`;

const RoomInfo = styled.div`
  flex: 1;
  min-width: 0;
`;

const RoomName = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
  color: ${appTheme.colors.text.primary};
  font-size: 14px;
  margin-bottom: 2px;
  flex-wrap: wrap;
`;

const RoomLastMessage = styled.div`
  font-size: 12px;
  color: ${appTheme.colors.text.secondary};
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  display: flex;
  align-items: center;
  gap: 4px;
  line-height: 1.3;

  /* Mention styling in chat preview */
  .mention[data-mention="true"] {
    background-color: #e3f2fd;
    color: #1976d2;
    padding: 1px 4px;
    border-radius: 3px;
    font-weight: 500;
    font-size: 11px;
    display: inline;
  }

  /* HTML content styling for preview */
  .header, .subheader {
    font-weight: 600;
    color: ${appTheme.colors.text.primary};
  }

  .content {
    font-size: 11px;
  }

  /* Handle nested HTML elements */
  * {
    font-size: inherit;
    line-height: inherit;
    margin: 0;
    padding: 0;
  }

  /* Ensure text doesn't break layout */
  span, div, p {
    display: inline;
    white-space: nowrap;
  }

  /* Style for special content indicators */
  strong, b {
    font-weight: 600;
    color: ${appTheme.colors.text.primary};
  }

  /* Emoji and special characters */
  .emoji {
    font-size: 14px;
  }
`;

const MessageTypeIndicator = styled.span`
  font-size: 11px;
  font-weight: 600;
  color: ${appTheme.colors.primary};
  background: ${appTheme.colors.primaryLight};
  padding: 2px 6px;
  border-radius: 8px;
  text-transform: uppercase;
  flex-shrink: 0;
`;

const RoomMeta = styled.div`
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 2px;
`;

const RoomTime = styled.div`
  font-size: 11px;
  color: ${appTheme.colors.text.light};
`;

const UnreadBadge = styled.div`
  background: ${appTheme.colors.primary};
  color: white;
  border-radius: 10px;
  padding: 2px 6px;
  font-size: 11px;
  font-weight: 500;
  min-width: 18px;
  text-align: center;
`;

const BotBadge = styled.div`
  display: flex;
  align-items: center;
  gap: 3px;
  font-size: 10px;
  color: #8b5cf6;
  background: #f3f4f6;
  padding: 3px 6px;
  border-radius: 10px;
  font-weight: 600;
  border: 1px solid #e5e7eb;
  white-space: nowrap;
`;

// Notification dot for tabs with unread messages
const TabNotificationDot = styled.div<{ $show: boolean }>`
  position: absolute;
  top: 8px;
  right: 8px;
  width: 8px;
  height: 8px;
  background: #ef4444; /* Red color for notifications */
  border-radius: 50%;
  border: 2px solid ${appTheme.colors.background.main};
  opacity: ${props => (props.$show ? 1 : 0)};
  transform: ${props => (props.$show ? 'scale(1)' : 'scale(0)')};
  transition: all 0.2s ease-in-out;
  animation: ${props => (props.$show ? 'pulse 2s infinite' : 'none')};
  z-index: 1;

  @keyframes pulse {
    0% {
      transform: scale(1);
      opacity: 1;
    }
    50% {
      transform: scale(1.3);
      opacity: 0.7;
    }
    100% {
      transform: scale(1);
      opacity: 1;
    }
  }

  /* Mobile - slightly larger for better visibility */
  @media (max-width: ${appTheme.breakpoints.md}) {
    width: 10px;
    height: 10px;
    top: 6px;
    right: 6px;
  }
`;

// Notification badge with count for tabs with unread messages
const TabNotificationBadge = styled.div<{ $show: boolean; $count: number }>`
  position: absolute;
  top: 4px;
  right: 4px;
  min-width: 16px;
  height: 16px;
  background: #ef4444; /* Red color for notifications */
  border-radius: 8px;
  border: 2px solid ${appTheme.colors.background.main};
  display: ${props => (props.$show ? 'flex' : 'none')};
  align-items: center;
  justify-content: center;
  font-size: 10px;
  font-weight: 600;
  color: white;
  padding: 0 4px;
  opacity: ${props => (props.$show ? 1 : 0)};
  transform: ${props => (props.$show ? 'scale(1)' : 'scale(0)')};
  transition: all 0.2s ease-in-out;
  animation: ${props => (props.$show ? 'pulse 2s infinite' : 'none')};
  z-index: 1;
  line-height: 1;

  @keyframes pulse {
    0% {
      transform: scale(1);
      opacity: 1;
    }
    50% {
      transform: scale(1.1);
      opacity: 0.8;
    }
    100% {
      transform: scale(1);
      opacity: 1;
    }
  }

  /* Mobile - slightly larger for better visibility */
  @media (max-width: ${appTheme.breakpoints.md}) {
    min-width: 18px;
    height: 18px;
    font-size: 11px;
    top: 2px;
    right: 2px;
  }

  /* Hide if count is too large to fit nicely */
  ${props =>
    props.$count > 99 &&
    `
    font-size: 8px;

    @media (max-width: ${appTheme.breakpoints.md}) {
      font-size: 9px;
    }
  `}
`;

const LoadingContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: ${appTheme.spacing.xl};
  color: ${appTheme.colors.text.secondary};
`;

const LoadingSpinner = styled.div`
  width: 32px;
  height: 32px;
  border: 3px solid ${appTheme.colors.border};
  border-top: 3px solid ${appTheme.colors.primary};
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: ${appTheme.spacing.md};

  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }
`;

const LoadingText = styled.div`
  font-size: 14px;
`;

// Skeleton Loading Components
const SkeletonContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${appTheme.spacing.sm};
  padding: ${appTheme.spacing.md};
`;

const SkeletonRoomItem = styled.div`
  display: flex;
  align-items: center;
  gap: ${appTheme.spacing.md};
  padding: ${appTheme.spacing.md};
  border-radius: ${appTheme.borderRadius.md};
`;

const SkeletonAvatar = styled.div`
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
  flex-shrink: 0;

  @keyframes shimmer {
    0% {
      background-position: -200% 0;
    }
    100% {
      background-position: 200% 0;
    }
  }
`;

const SkeletonContent = styled.div`
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: ${appTheme.spacing.xs};
`;

const SkeletonName = styled.div<{ $width: string }>`
  height: 16px;
  width: ${props => props.$width};
  border-radius: 4px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;

  @keyframes shimmer {
    0% {
      background-position: -200% 0;
    }
    100% {
      background-position: 200% 0;
    }
  }
`;

const SkeletonMessage = styled.div<{ $width: string }>`
  height: 12px;
  width: ${props => props.$width};
  border-radius: 4px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;

  @keyframes shimmer {
    0% {
      background-position: -200% 0;
    }
    100% {
      background-position: 200% 0;
    }
  }
`;

const SkeletonTime = styled.div`
  width: 40px;
  height: 10px;
  border-radius: 4px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;

  @keyframes shimmer {
    0% {
      background-position: -200% 0;
    }
    100% {
      background-position: 200% 0;
    }
  }
`;

const SkeletonInfo = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  flex: 1;
`;

const SkeletonDetails = styled.div`
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: ${appTheme.spacing.xs};
`;

const RoomSkeletonLoader = () => {
  const skeletonItems = [
    { nameWidth: '60%', messageWidth: '80%' },
    { nameWidth: '45%', messageWidth: '65%' },
    { nameWidth: '70%', messageWidth: '90%' },
    { nameWidth: '55%', messageWidth: '75%' },
    { nameWidth: '65%', messageWidth: '85%' },
  ];

  return (
    <SkeletonContainer>
      {skeletonItems.map((item, index) => (
        <SkeletonRoomItem key={index}>
          <SkeletonAvatar />
          <SkeletonInfo>
            <SkeletonDetails>
              <SkeletonName $width={item.nameWidth} />
              <SkeletonMessage $width={item.messageWidth} />
            </SkeletonDetails>
            <SkeletonTime />
          </SkeletonInfo>
        </SkeletonRoomItem>
      ))}
    </SkeletonContainer>
  );
};

export default function ChatSidebar({
  rooms,
  activeTab,
  selectedRoom,
  searchQuery,
  loading = false,
  currentUser,
  onTabChange,
  onRoomSelect,
  onSearchChange,
  onCreateChat,
  onRefreshChats,
  isMobileOpen = false,
  onMobileClose,
}: ChatSidebarProps) {
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  // Handle visibility change to refresh chat list when user returns to tab
  useEffect(() => {
    let refreshTimeout: NodeJS.Timeout;

    const debouncedRefresh = () => {
      // Clear any existing timeout
      if (refreshTimeout) {
        clearTimeout(refreshTimeout);
      }

      // Set a new timeout to debounce rapid visibility changes
      refreshTimeout = setTimeout(() => {
        if (onRefreshChats) {
          onRefreshChats();
        }
      }, 500); // 500ms debounce
    };

    const handleVisibilityChange = () => {
      // Only refresh if the document becomes visible and we have a refresh function
      if (!document.hidden && onRefreshChats) {
        debouncedRefresh();
      }
    };

    const handleFocus = () => {
      // Refresh when window gains focus
      if (onRefreshChats) {
        debouncedRefresh();
      }
    };

    // Add event listeners for visibility and focus changes
    document.addEventListener('visibilitychange', handleVisibilityChange);
    window.addEventListener('focus', handleFocus);

    // Cleanup event listeners on unmount
    return () => {
      if (refreshTimeout) {
        clearTimeout(refreshTimeout);
      }
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      window.removeEventListener('focus', handleFocus);
    };
  }, [onRefreshChats]);

  const tabs = [
    { key: 'private' as RoomType, label: 'ส่วนตัว', icon: MessageCircle },
    { key: 'task' as RoomType, label: 'Task', icon: CheckSquare },
    { key: 'department' as RoomType, label: 'แผนก', icon: Users },
    { key: 'organization' as RoomType, label: 'องค์กร', icon: Building2 },
  ];

  const currentRooms = rooms[activeTab].filter(room =>
    room.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // Calculate unread counts for each tab type
  const getTabUnreadCount = (tabType: RoomType): number => {
    return rooms[tabType].reduce((total, room) => {
      return total + (room.unreadCount || 0);
    }, 0);
  };

  const getRoomIcon = (type: RoomType) => {
    switch (type) {
      case 'private':
        return <MessageCircle size={16} />;
      case 'task':
        return <CheckSquare size={16} />;
      case 'department':
        return <Users size={16} />;
      case 'organization':
        return <Building2 size={16} />;
      default:
        return <MessageCircle size={16} />;
    }
  };

  const getRoomPrefix = (type: RoomType) => {
    switch (type) {
      case 'private':
        return '';
      case 'task':
        return '#';
      case 'department':
        return '';
      case 'organization':
        return '';
      default:
        return '';
    }
  };

  const formatTime = (timeString?: string) => {
    if (!timeString || !isClient) return '';
    return formatSimpleTimestamp(timeString);
  };

  // Helper function to process message content for mentions in preview
  const processMessagePreview = (content?: string): string => {
    if (!content) return '';
    // Convert mention format to HTML for display
    return replaceMentionsWithHTML(content);
  };

  // Helper function to detect if content is complex HTML
  const isComplexHtml = (content: string): boolean => {
    if (!content) return false;

    // Check for HTML document structure
    const hasHtmlStructure = content.includes('<html>') || content.includes('<head>') || content.includes('<body>');

    // Check for complex HTML elements
    const complexElements = ['<div', '<p', '<ul', '<ol', '<li', '<h1', '<h2', '<h3', '<h4', '<h5', '<h6', '<style', '<table'];
    const hasComplexElements = complexElements.some(element => content.includes(element));

    return hasHtmlStructure || hasComplexElements;
  };

  // Enhanced function to extract meaningful preview from complex HTML
  const extractHtmlPreview = (htmlContent: string): string => {
    if (!htmlContent) return '';

    try {
      // สร้าง temporary DOM element
      const tempDiv = document.createElement('div');
      tempDiv.innerHTML = htmlContent;

      // ลบ style tags และ script tags
      const styleTags = tempDiv.querySelectorAll('style, script');
      styleTags.forEach(tag => tag.remove());

      // หาข้อความสำคัญจาก headers และ content
      let preview = '';

      // ลองหา header หรือ title ก่อน
      const headers = tempDiv.querySelectorAll('h1, h2, h3, h4, h5, h6, .header, .subheader');
      if (headers.length > 0) {
        const headerText = headers[0].textContent?.trim();
        if (headerText) {
          preview = headerText;
        }
      }

      // ถ้าไม่มี header ให้หาจาก content หรือ paragraph แรก
      if (!preview) {
        const contentElements = tempDiv.querySelectorAll('p, .content, div');
        for (const element of contentElements) {
          const text = element.textContent?.trim();
          if (text && text.length > 10) {
            preview = text;
            break;
          }
        }
      }

      // ถ้ายังไม่มี ให้ใช้ text content ทั้งหมด
      if (!preview) {
        preview = tempDiv.textContent?.trim() || '';
      }

      // ทำความสะอาดและจำกัดความยาว
      preview = preview.replace(/\s+/g, ' ').trim();

      // จำกัดความยาวและเพิ่ม ellipsis
      const maxLength = 60;
      if (preview.length > maxLength) {
        const truncated = preview.substring(0, maxLength);
        const lastSpace = truncated.lastIndexOf(' ');
        if (lastSpace > maxLength * 0.7) {
          preview = truncated.substring(0, lastSpace) + '...';
        } else {
          preview = truncated + '...';
        }
      }

      return preview;
    } catch (error) {
      console.warn('Error processing HTML preview:', error);
      // Fallback to simple text extraction
      return htmlContent.replace(/<[^>]*>/g, ' ').replace(/\s+/g, ' ').trim().substring(0, 60) + '...';
    }
  };

  const getRoomDisplayName = (room: Room) => {
    if (room.type === 'private' && room.chatUsers && currentUser) {
      // Filter out current user and show other users' names
      const otherUsers = room.chatUsers.filter(cu => cu.user.id !== currentUser.id);
      if (otherUsers.length > 0) {
        return otherUsers.map(cu => `${cu.user.firstName} ${cu.user.lastName}`).join(', ');
      }
      return 'Private Chat';
    }
    return room.name;
  };

  const getPrivateChatAvatar = (room: Room) => {
    if (room.type === 'private' && room.chatUsers && currentUser) {
      // Get the other user (not current user)
      const otherUsers = room.chatUsers.filter(cu => cu.user.id !== currentUser.id);
      if (otherUsers.length > 0) {
        const otherUser = otherUsers[0].user;
        // If user has a profile image, use it; otherwise show initials
        const userAvatar = otherUsers[0].user?.imageUrl || ''; // Assuming avatar URL is stored in room.avatar
        if (userAvatar) {
          return (
            <ProfileImage src={userAvatar} alt={`${otherUser.firstName} ${otherUser.lastName}`} />
          );
        } else {
          // Show initials as fallback
          const initials = `${otherUser.firstName.charAt(0)}${otherUser.lastName.charAt(0)}`;
          return <ProfileInitials>{initials}</ProfileInitials>;
        }
      }
    }
    return getRoomIcon(room.type);
  };

  const getMessageTypeTitle = (messageType?: string | null) => {
    if (!messageType || messageType.toLowerCase() === 'text') return null;

    switch (messageType.toLowerCase()) {
      case 'file':
        return 'ไฟล์';
      case 'image':
        return 'รูปภาพ';
      case 'link':
        return 'ลิงค์';
      case 'video':
        return 'วิดีโอ';
      case 'audio':
        return 'เสียง';
      case 'location':
        return 'ตำแหน่ง';
      case 'sticker':
        return 'สติกเกอร์';
      case 'gif':
        return 'GIF';
      default:
        return messageType;
    }
  };

  // ฟังก์ชันแปลง HTML string ให้เป็นข้อความสำหรับแสดงใน preview (รักษา mentions)
  const formatHtmlToPreview = (htmlContent: string): string => {
    if (!htmlContent) return '';

    // ตรวจสอบว่าเป็น complex HTML หรือไม่
    if (isComplexHtml(htmlContent)) {
      return extractHtmlPreview(htmlContent);
    }

    // First, process mentions if the content doesn't already contain HTML
    let processedContent = htmlContent;
    if (!htmlContent.includes('<')) {
      processedContent = processMessagePreview(htmlContent);
    }

    // สร้าง temporary DOM element เพื่อแปลง HTML
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = processedContent;

    // จัดการ HTML elements ต่างๆ
    // จัดการ headers
    const headers = tempDiv.querySelectorAll('h1, h2, h3, h4, h5, h6, .header, .subheader');
    headers.forEach(header => {
      const text = header.textContent || '';
      if (text.trim()) {
        header.textContent = `${text.trim()} `;
      }
    });

    // จัดการ paragraphs
    const paragraphs = tempDiv.querySelectorAll('p');
    paragraphs.forEach(p => {
      const text = p.textContent || '';
      if (text.trim()) {
        p.textContent = `${text.trim()} `;
      }
    });

    // จัดการ bullet list และ ordered list
    const listItems = tempDiv.querySelectorAll('li');
    listItems.forEach((li, index) => {
      const text = li.textContent || '';
      if (text.trim()) {
        const parentList = li.closest('ul, ol');
        const isOrderedList = parentList?.tagName.toLowerCase() === 'ol';
        const prefix = isOrderedList ? `${index + 1}. ` : '• ';
        li.textContent = `${prefix}${text.trim()} `;
      }
    });

    // จัดการ divs และ spans
    const divs = tempDiv.querySelectorAll('div:not(.mention)');
    divs.forEach(div => {
      const text = div.textContent || '';
      if (text.trim()) {
        div.textContent = `${text.trim()} `;
      }
    });

    // แทนที่ <br> ด้วย space
    const brElements = tempDiv.querySelectorAll('br');
    brElements.forEach(br => {
      br.replaceWith(' ');
    });

    // Check if there are mentions to preserve
    const hasMentions = tempDiv.querySelector('.mention[data-mention="true"]');

    if (!hasMentions) {
      // ได้ข้อความธรรมดา
      let plainText = tempDiv.textContent || tempDiv.innerText || '';
      // ลบ whitespace ที่เกินมาและ line breaks
      plainText = plainText.replace(/\s+/g, ' ').trim();

      // สำหรับ HTML ที่ซับซ้อน ให้แสดงข้อความเริ่มต้นที่สำคัญ
      if (plainText.length > 100) {
        // หาข้อความสำคัญจากส่วนแรก
        const sentences = plainText.split(/[.!?。]/);
        let preview = '';
        for (const sentence of sentences) {
          if (preview.length + sentence.length < 80) {
            preview += sentence.trim() + ' ';
          } else {
            break;
          }
        }
        if (preview.trim()) {
          return preview.trim() + '...';
        }
      }

      // จำกัดความยาวสำหรับ preview
      const maxLength = 80;
      if (plainText.length > maxLength) {
        plainText = plainText.substring(0, maxLength) + '...';
      }
      return plainText;
    } else {
      // Return HTML with mentions preserved
      let result = tempDiv.innerHTML;
      // Clean up extra whitespace
      result = result.replace(/\s+/g, ' ').trim();
      // Limit length for HTML content
      const maxLength = 100;
      if (result.length > maxLength) {
        const truncated = result.substring(0, maxLength);
        const lastSpace = truncated.lastIndexOf(' ');
        if (lastSpace > maxLength * 0.7) {
          result = truncated.substring(0, lastSpace) + '...';
        } else {
          result = truncated + '...';
        }
      }
      return result;
    }
  };

  const formatLastMessage = (room: Room) => {
    const messageTypeTitle = getMessageTypeTitle(room.lastMessageType);

    // สำหรับ message type TEXT ให้แสดงเนื้อหา message โดยแปลง HTML เป็น plain text
    if (!messageTypeTitle) {
      const formattedContent = formatHtmlToPreview(room.lastMessage || '');

      // ตรวจสอบว่าเป็น HTML content หรือไม่
      const isHtmlContent = formattedContent.includes('<') || (room.lastMessage && room.lastMessage.includes('<'));

      if (isHtmlContent) {
        // สำหรับ HTML content ให้ render ด้วย dangerouslySetInnerHTML
        return <span dangerouslySetInnerHTML={{ __html: formattedContent }} />;
      }

      // สำหรับ plain text
      return <span>{formattedContent}</span>;
    }

    // สำหรับ message type อื่น ๆ ให้แสดง indicator พร้อมเนื้อหา (ถ้ามี)
    return (
      <>
        {room.lastMessageType?.toLowerCase() !== "text" && (
          <MessageTypeIndicator>{messageTypeTitle}</MessageTypeIndicator>
        )}
        {room.lastMessage && room.lastMessageType?.toLowerCase() === 'text' && (() => {
          const formattedContent = formatHtmlToPreview(room.lastMessage);
          const isHtmlContent = formattedContent.includes('<') || room.lastMessage.includes('<');

          if (isHtmlContent) {
            return <span dangerouslySetInnerHTML={{ __html: formattedContent }} />;
          }
          return <span>{formattedContent}</span>;
        })()}
      </>
    );
  };

  type MessageType = 'text' | 'file' | 'image' | 'sticker' | 'link';
  type MessageStatus = 'sending' | 'delivered' | 'read' | 'failed';
  type ChatType = 'private' | 'task' | 'department' | 'organization';

  return (
    <>
      <MobileOverlay $isVisible={isMobileOpen} onClick={onMobileClose} />
      <SidebarContainer className={isMobileOpen ? 'mobile-open' : ''}>
        <SidebarHeader>
          <HeaderRow>
            <HeaderTitle>Chat</HeaderTitle>
            {/* <MobileCloseButton onClick={onMobileClose}>
              <X size={18} />
            </MobileCloseButton> */}
            {activeTab === 'private' && (
              <CreateButton onClick={() => onCreateChat(activeTab)}>
                <Plus size={14} />
              </CreateButton>
            )}
          </HeaderRow>
          {/* <SearchInput
          type="text"
          placeholder="ค้นหาการสนทนา..."
          value={searchQuery}
          onChange={e => onSearchChange(e.target.value)}
        /> */}
        </SidebarHeader>

        <TabsContainer>
          {tabs.map(tab => {
            const unreadCount = getTabUnreadCount(tab.key);
            const showNotification = unreadCount > 0;

            return (
              <Tab
                key={tab.key}
                $isActive={activeTab === tab.key}
                $isLoading={loading && activeTab === tab.key}
                onClick={() => onTabChange(tab.key)}
                disabled={loading && activeTab === tab.key}
                title={`${tab.label}${showNotification ? ` (${unreadCount} unread)` : ''}`}
              >
                <tab.icon size={18} />
                {/* Show badge with count for small numbers, dot for large numbers or when count is 0 */}
                {showNotification &&
                  (unreadCount <= 99 ? (
                    <TabNotificationBadge $show={showNotification} $count={unreadCount}>
                      {unreadCount}
                    </TabNotificationBadge>
                  ) : (
                    <TabNotificationDot $show={showNotification} />
                  ))}
              </Tab>
            );
          })}
        </TabsContainer>

        <RoomList>
          {loading ? (
            <RoomSkeletonLoader />
          ) : (
            <>
              {currentRooms.map(room => (
                <RoomItem
                  key={room.id}
                  $isActive={selectedRoom?.id === room.id}
                  onClick={() => onRoomSelect(room)}
                >
                  <RoomAvatar $type={room.type}>{getPrivateChatAvatar(room)}</RoomAvatar>
                  <RoomInfo>
                    <RoomName>
                      {getRoomPrefix(room.type)}
                      {getRoomDisplayName(room)}
                      {room.isBot && (
                        <BotBadge>
                          <Bot size={11} />
                          {room.botDuration || 30}min
                        </BotBadge>
                      )}
                    </RoomName>
                    <RoomLastMessage>{formatLastMessage(room)}</RoomLastMessage>
                  </RoomInfo>
                  <RoomMeta>
                    <RoomTime>{formatTime(room.lastMessageTime)}</RoomTime>
                    {room.unreadCount !== undefined && room.unreadCount > 0 && (
                      <UnreadBadge>{room.unreadCount}</UnreadBadge>
                    )}
                  </RoomMeta>
                </RoomItem>
              ))}

              {!loading && currentRooms.length === 0 && (
                <div
                  style={{
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center',
                    justifyContent: 'center',
                    padding: `${appTheme.spacing.xl} ${appTheme.spacing.lg}`,
                    textAlign: 'center',
                    color: appTheme.colors.text.secondary,
                    minHeight: '200px',
                  }}
                >
                  <Search size={32} style={{ marginBottom: appTheme.spacing.md, opacity: 0.5 }} />
                  <div>ไม่พบ Chat {activeTab}</div>
                </div>
              )}
            </>
          )}
        </RoomList>
      </SidebarContainer>
    </>
  );
}

'use client';

import React, { useState, useRef, useEffect } from 'react';
import styled from 'styled-components';
import { Check, Check<PERSON>he<PERSON>, Eye, Users } from 'lucide-react';
import { appTheme } from '@/app/theme';
import { formatReadTime } from '@/utils/dateUtils';

interface MessageReadUser {
  id: number;
  firstName: string;
  lastName: string;
  imageUrl?: string;
}

interface MessageReadStatus {
  readByUsers: Array<{
    user: MessageReadUser;
    readAt: string;
  }>;
  unreadUsers: MessageReadUser[];
  totalParticipants: number;
  readCount: number;
  unreadCount: number;
}

interface ReadStatusProps {
  readStatus?: MessageReadStatus;
  isOwn: boolean;
  isGroupChat: boolean;
  messageId: string;
}

const ReadStatusContainer = styled.div<{ $isOwn: boolean }>`
  display: flex;
  align-items: center;
  gap: ${appTheme.spacing.xs};
  margin: 0; /* Remove margin since it's now controlled by parent MessageFooter */
  justify-content: ${props => (props.$isOwn ? 'flex-end' : 'flex-start')};
  opacity: 0.8;
  transition: opacity 0.2s ease;

  &:hover {
    opacity: 1;
  }

  /* Mobile - better spacing */
  @media (max-width: ${appTheme.breakpoints.md}) {
    gap: ${appTheme.spacing.sm};
  }
`;

const ReadIndicator = styled.div<{ $hasReads: boolean; $isOwn: boolean }>`
  display: flex;
  align-items: center;
  gap: ${appTheme.spacing.xs};
  color: ${props => {
    if (!props.$isOwn) return 'transparent';
    return props.$hasReads ? '#10b981' : appTheme.colors.text.light;
  }};
  font-size: 12px;
  cursor: ${props => (props.$hasReads ? 'pointer' : 'default')};
  transition: all 0.2s ease;
  position: relative; /* Add relative positioning for tooltip */

  &:hover {
    color: ${props => {
      if (!props.$isOwn) return 'transparent';
      return props.$hasReads ? '#059669' : appTheme.colors.text.secondary;
    }};
  }

  /* Mobile - slightly larger for better readability */
  @media (max-width: ${appTheme.breakpoints.md}) {
    font-size: 13px;
  }
`;

const ReadCount = styled.span`
  font-weight: 500;
  min-width: 16px;
  text-align: center;
`;

const ReadTooltip = styled.div<{ $show: boolean }>`
  position: absolute;
  bottom: calc(100% + ${appTheme.spacing.sm}); /* Position above the footer */
  right: 0;
  background: ${appTheme.colors.background.main};
  border: 1px solid ${appTheme.colors.border};
  border-radius: ${appTheme.borderRadius.md};
  padding: ${appTheme.spacing.sm};
  box-shadow: ${appTheme.shadows.lg};
  z-index: 1000;
  min-width: 200px;
  max-width: 300px;
  opacity: ${props => (props.$show ? 1 : 0)};
  visibility: ${props => (props.$show ? 'visible' : 'hidden')};
  transform: translateY(${props => (props.$show ? '0' : '10px')});
  transition: all 0.2s ease;
  pointer-events: ${props => (props.$show ? 'auto' : 'none')};

  /* Tablet - adjust positioning for better visibility */
  @media (max-width: ${appTheme.breakpoints.lg}) and (min-width: ${appTheme.breakpoints.md}) {
    bottom: calc(100% + ${appTheme.spacing.md});
    min-width: 220px;
    max-width: 280px;
  }

  /* Mobile - adjust positioning */
  @media (max-width: ${appTheme.breakpoints.md}) {
    position: fixed;
    bottom: auto;
    top: 50%;
    left: 50%;
    right: auto;
    transform: ${props =>
      props.$show
        ? 'translate(-50%, -50%)'
        : 'translate(-50%, -50%) scale(0.9)'
    };
    min-width: 280px;
    max-width: 320px;
  }
`;

const TooltipSection = styled.div`
  margin-bottom: ${appTheme.spacing.sm};

  &:last-child {
    margin-bottom: 0;
  }
`;

const TooltipTitle = styled.h4`
  font-size: 13px;
  font-weight: 600;
  color: ${appTheme.colors.text.primary};
  margin: 0 0 ${appTheme.spacing.xs} 0;
  display: flex;
  align-items: center;
  gap: ${appTheme.spacing.xs};
`;

const UserList = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${appTheme.spacing.xs};
  max-height: 150px;
  overflow-y: auto;

  &::-webkit-scrollbar {
    width: 4px;
  }

  &::-webkit-scrollbar-thumb {
    background: ${appTheme.colors.border};
    border-radius: 2px;
  }
`;

const UserItem = styled.div`
  display: flex;
  align-items: center;
  gap: ${appTheme.spacing.sm};
  padding: ${appTheme.spacing.xs};
  border-radius: ${appTheme.borderRadius.sm};
  transition: background 0.2s ease;

  &:hover {
    background: ${appTheme.colors.background.lighter};
  }
`;

const UserAvatar = styled.div<{ $imageUrl?: string }>`
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  font-weight: 600;
  color: white;
  flex-shrink: 0;
  background: ${props =>
    props.$imageUrl && props.$imageUrl.trim() !== ''
      ? `url(${props.$imageUrl})`
      : `linear-gradient(135deg, ${appTheme.colors.primary} 0%, #667eea 100%)`};
  background-size: cover;
  background-position: center;
  border: 1px solid white;
  box-shadow: ${appTheme.shadows.sm};
`;

const UserInfo = styled.div`
  flex: 1;
  min-width: 0;
`;

const UserName = styled.div`
  font-size: 12px;
  font-weight: 500;
  color: ${appTheme.colors.text.primary};
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
`;

const ReadTime = styled.div`
  font-size: 11px;
  color: ${appTheme.colors.text.light};
  margin-top: 1px;
`;

const EmptyState = styled.div`
  font-size: 12px;
  color: ${appTheme.colors.text.light};
  font-style: italic;
  padding: ${appTheme.spacing.xs};
  text-align: center;
`;

const Overlay = styled.div<{ $show: boolean }>`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999;
  opacity: ${props => (props.$show ? 1 : 0)};
  visibility: ${props => (props.$show ? 'visible' : 'hidden')};
  transition: all 0.2s ease;

  /* Mobile - visible overlay with backdrop */
  @media (max-width: ${appTheme.breakpoints.md}) {
    background: rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(2px);
  }

  /* Desktop - invisible overlay for click detection */
  @media (min-width: ${appTheme.breakpoints.md}) {
    background: transparent;
    backdrop-filter: none;
  }
`;

const ReadStatus: React.FC<ReadStatusProps> = ({
  readStatus,
  isOwn,
  isGroupChat,
  messageId
}) => {
  const [showTooltip, setShowTooltip] = useState(false);
  const readStatusRef = useRef<HTMLDivElement>(null);

  // Handle click outside to close tooltip
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent | TouchEvent) => {
      if (readStatusRef.current && !readStatusRef.current.contains(event.target as Node)) {
        setShowTooltip(false);
      }
    };

    const handleMouseDown = (event: MouseEvent) => handleClickOutside(event);
    const handleTouchStart = (event: TouchEvent) => handleClickOutside(event);

    // Add event listener when tooltip is shown
    if (showTooltip) {
      document.addEventListener('mousedown', handleMouseDown);
      document.addEventListener('touchstart', handleTouchStart);
    }

    // Cleanup event listeners
    return () => {
      document.removeEventListener('mousedown', handleMouseDown);
      document.removeEventListener('touchstart', handleTouchStart);
    };
  }, [showTooltip]);

  // Handle escape key to close tooltip
  useEffect(() => {
    const handleEscapeKey = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        setShowTooltip(false);
      }
    };

    // Add event listener when tooltip is shown
    if (showTooltip) {
      document.addEventListener('keydown', handleEscapeKey);
    }

    // Cleanup event listener
    return () => {
      document.removeEventListener('keydown', handleEscapeKey);
    };
  }, [showTooltip]);

  // Don't show read status for messages from others in individual chats
  if (!isOwn && !isGroupChat) {
    return null;
  }

  // Don't show read status if no read status data is available
  if (!readStatus) {
    return null;
  }

  const { readByUsers, unreadUsers, readCount, totalParticipants } = readStatus;
  const hasReads = readCount > 0;



  const getInitials = (firstName: string, lastName: string) => {
    return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase();
  };

  const handleToggleTooltip = () => {
    if (hasReads) {
      setShowTooltip(!showTooltip);
    }
  };

  const handleCloseTooltip = () => {
    setShowTooltip(false);
  };

  return (
    <>
      <Overlay $show={showTooltip} onClick={handleCloseTooltip} />
      <ReadStatusContainer $isOwn={isOwn} ref={readStatusRef}>
        <ReadIndicator
          $hasReads={hasReads}
          $isOwn={isOwn}
          onClick={handleToggleTooltip}
        >
          {isGroupChat ? (
            <>
              {hasReads ? <CheckCheck size={14} /> : <Check size={14} />}
              {hasReads && (
                <ReadCount>
                  {readCount}{totalParticipants > 0 && `/${totalParticipants}`}
                </ReadCount>
              )}
            </>
          ) : (
            <>
              {hasReads ? <CheckCheck size={14} /> : <Check size={14} />}
            </>
          )}

          {hasReads && showTooltip && (
            <ReadTooltip
              $show={showTooltip}
              onClick={(e) => e.stopPropagation()} // Prevent closing when clicking inside tooltip
            >
              {readByUsers.length > 0 && (
                <TooltipSection>
                  <TooltipTitle>
                    <Eye size={12} />
                    Read by ({readByUsers.length})
                  </TooltipTitle>
                  <UserList>
                    {readByUsers.map(({ user, readAt }) => (
                      <UserItem key={user.id}>
                        <UserAvatar $imageUrl={user.imageUrl}>
                          {!user.imageUrl && getInitials(user.firstName, user.lastName)}
                        </UserAvatar>
                        <UserInfo>
                          <UserName>{user.firstName} {user.lastName}</UserName>
                          <ReadTime>{formatReadTime(readAt)}</ReadTime>
                        </UserInfo>
                      </UserItem>
                    ))}
                  </UserList>
                </TooltipSection>
              )}

              {unreadUsers.length > 0 && isGroupChat && (
                <TooltipSection>
                  <TooltipTitle>
                    <Users size={12} />
                    Not read yet ({unreadUsers.length})
                  </TooltipTitle>
                  <UserList>
                    {unreadUsers.map((user) => (
                      <UserItem key={user.id}>
                        <UserAvatar $imageUrl={user.imageUrl}>
                          {!user.imageUrl && getInitials(user.firstName, user.lastName)}
                        </UserAvatar>
                        <UserInfo>
                          <UserName>{user.firstName} {user.lastName}</UserName>
                        </UserInfo>
                      </UserItem>
                    ))}
                  </UserList>
                </TooltipSection>
              )}

              {readByUsers.length === 0 && unreadUsers.length === 0 && (
                <EmptyState>No read status available</EmptyState>
              )}
            </ReadTooltip>
          )}
        </ReadIndicator>
      </ReadStatusContainer>
    </>
  );
};

export default ReadStatus;

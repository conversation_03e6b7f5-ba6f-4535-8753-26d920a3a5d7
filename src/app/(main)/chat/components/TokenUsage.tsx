'use client';

import React, { useState } from 'react';
import styled from 'styled-components';
import { BarChart3, <PERSON>, EyeOff, DollarSign } from 'lucide-react';
import { appTheme } from '@/app/theme';

interface TokenUsageProps {
  promptTokens?: number;
  completionTokens?: number;
  totalTokens?: number;
  modelName?: string;
  priceInput?: number;  // Price per 1M input tokens
  priceOutput?: number; // Price per 1M output tokens
  className?: string;
}

const TokenUsageContainer = styled.div`
  margin-top: ${appTheme.spacing.xs};
  font-size: 11px;
  color: ${appTheme.colors.text.secondary};
`;

const TokenUsageToggle = styled.button`
  display: flex;
  align-items: center;
  gap: ${appTheme.spacing.xs};
  background: none;
  border: none;
  color: ${appTheme.colors.text.secondary};
  cursor: pointer;
  font-size: 11px;
  padding: ${appTheme.spacing.xs} 0;
  transition: color 0.2s ease;

  &:hover {
    color: ${appTheme.colors.text.primary};
  }

  svg {
    width: 12px;
    height: 12px;
  }
`;

const TokenUsageDetails = styled.div`
  background: ${appTheme.colors.background.lighter};
  border: 1px solid ${appTheme.colors.border};
  border-radius: 6px;
  padding: ${appTheme.spacing.sm};
  margin-top: ${appTheme.spacing.xs};
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
`;

const TokenRow = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: ${appTheme.spacing.xs};

  &:last-child {
    margin-bottom: 0;
  }
`;

const TokenLabel = styled.span`
  color: ${appTheme.colors.text.secondary};
`;

const TokenValue = styled.span`
  color: ${appTheme.colors.text.primary};
  font-weight: 500;
`;

const CostRow = styled(TokenRow)`
  border-top: 1px solid ${appTheme.colors.border};
  padding-top: ${appTheme.spacing.xs};
  margin-top: ${appTheme.spacing.xs};
  margin-bottom: 0;
`;

const CostValue = styled(TokenValue)`
  color: ${appTheme.colors.primary};
  display: flex;
  align-items: center;
  gap: 2px;

  svg {
    width: 10px;
    height: 10px;
  }
`;

const ModelInfo = styled.div`
  font-size: 10px;
  color: ${appTheme.colors.text.light};
  margin-bottom: ${appTheme.spacing.xs};
  font-style: italic;
`;

export default function TokenUsage({
  promptTokens,
  completionTokens,
  totalTokens,
  modelName,
  priceInput,
  priceOutput,
  className
}: TokenUsageProps) {
  const [isExpanded, setIsExpanded] = useState(false);

  // Calculate costs if pricing is available
  const calculateCost = () => {
    if (!priceInput || !priceOutput || !promptTokens || !completionTokens) {
      return null;
    }

    // Prices are per 1M tokens, so divide by 1,000,000
    const inputCost = (promptTokens / 1_000_000) * priceInput;
    const outputCost = (completionTokens / 1_000_000) * priceOutput;
    const totalCost = (inputCost + outputCost) * 1.89;

    return {
      inputCost,
      outputCost,
      totalCost
    };
  };

  const cost = calculateCost();

  // Don't render if no token data
  if (!totalTokens && !promptTokens && !completionTokens) {
    return null;
  }

  const formatCost = (amount: number) => {
    if (amount < 0.001) {
      return `<$0.001`;
    }
    return `$${amount.toFixed(3)}`;
  };

  return (
    <TokenUsageContainer className={className}>
      <TokenUsageToggle onClick={() => setIsExpanded(!isExpanded)}>
        {isExpanded ? <EyeOff /> : <Eye />}
        <BarChart3 />
        {totalTokens ? `${totalTokens.toLocaleString()} tokens` : 'Token usage'}
        {cost && ` • ${formatCost(cost.totalCost)}`}
      </TokenUsageToggle>

      {isExpanded && (
        <TokenUsageDetails>
          {modelName && (
            <ModelInfo>Model: {modelName}</ModelInfo>
          )}
          
          {promptTokens !== undefined && (
            <TokenRow>
              <TokenLabel>Prompt tokens:</TokenLabel>
              <TokenValue>{promptTokens.toLocaleString()}</TokenValue>
            </TokenRow>
          )}
          
          {completionTokens !== undefined && (
            <TokenRow>
              <TokenLabel>Completion tokens:</TokenLabel>
              <TokenValue>{completionTokens.toLocaleString()}</TokenValue>
            </TokenRow>
          )}
          
          {totalTokens !== undefined && (
            <TokenRow>
              <TokenLabel>Total tokens:</TokenLabel>
              <TokenValue>{totalTokens.toLocaleString()}</TokenValue>
            </TokenRow>
          )}

          {cost && (
            <CostRow>
              <TokenLabel>Estimated cost:</TokenLabel>
              <CostValue>
                <DollarSign />
                {formatCost(cost.totalCost)}
              </CostValue>
            </CostRow>
          )}
        </TokenUsageDetails>
      )}
    </TokenUsageContainer>
  );
}

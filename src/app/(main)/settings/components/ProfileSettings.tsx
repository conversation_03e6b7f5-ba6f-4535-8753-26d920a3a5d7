'use client';

import React, { useState, useEffect, useRef, useCallback } from 'react';
import styled from 'styled-components';
import { useRouter } from 'next/navigation';
import useUserStore from '@/store/userStore';
import { getCookie } from 'cookies-next';
import { appTheme as settingsTheme, appStyles as commonStyles } from '@/app/theme';
import { s3Service } from '@/services/s3Service';
import Cropper, { Area, Point } from 'react-easy-crop';

const ProfileContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${settingsTheme.spacing.xl};
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: ${settingsTheme.spacing.xl};
  background-color: ${settingsTheme.colors.background.main};
  border-radius: ${settingsTheme.borderRadius.lg};
  box-shadow: ${settingsTheme.shadows.md};

  /* Large desktop */
  @media (min-width: ${settingsTheme.breakpoints['2xl']}) {
    max-width: 1400px;
    padding: ${settingsTheme.spacing['2xl']};
    gap: ${settingsTheme.spacing['2xl']};
  }

  /* Tablet */
  @media (max-width: ${settingsTheme.breakpoints.lg}) and (min-width: ${settingsTheme.breakpoints.md}) {
    max-width: 100%;
    padding: ${settingsTheme.spacing.lg};
    gap: ${settingsTheme.spacing.lg};
    border-radius: ${settingsTheme.borderRadius.md};
  }

  /* Mobile */
  @media (max-width: ${settingsTheme.breakpoints.md}) {
    padding: ${settingsTheme.spacing.md};
    gap: ${settingsTheme.spacing.md};
    border-radius: ${settingsTheme.borderRadius.sm};
    box-shadow: ${settingsTheme.shadows.sm};
  }

  /* Small mobile */
  @media (max-width: ${settingsTheme.breakpoints.sm}) {
    padding: ${settingsTheme.spacing.sm};
    gap: ${settingsTheme.spacing.sm};
    border-radius: 0;
    box-shadow: none;
    background-color: transparent;
  }
`;

const SectionTitle = styled.h2`
  ${commonStyles.sectionTitle}
`;

const FormGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${settingsTheme.spacing.sm};
  margin-bottom: ${settingsTheme.spacing.lg};
  width: 100%;

  /* Tablet */
  @media (max-width: ${settingsTheme.breakpoints.lg}) and (min-width: ${settingsTheme.breakpoints.md}) {
    gap: ${settingsTheme.spacing.xs};
    margin-bottom: ${settingsTheme.spacing.md};
  }

  /* Mobile */
  @media (max-width: ${settingsTheme.breakpoints.md}) {
    gap: ${settingsTheme.spacing.xs};
    margin-bottom: ${settingsTheme.spacing.sm};
  }

  /* Small mobile */
  @media (max-width: ${settingsTheme.breakpoints.sm}) {
    margin-bottom: ${settingsTheme.spacing.xs};
  }
`;

const Label = styled.label`
  font-size: ${settingsTheme.typography.fontSizes.sm};
  font-weight: ${settingsTheme.typography.fontWeights.medium};
  color: ${settingsTheme.colors.text.secondary};
  margin-bottom: ${settingsTheme.spacing.xs};

  /* Mobile */
  @media (max-width: ${settingsTheme.breakpoints.md}) {
    font-size: ${settingsTheme.typography.fontSizes.base};
    font-weight: ${settingsTheme.typography.fontWeights.semibold};
  }

  /* Small mobile */
  @media (max-width: ${settingsTheme.breakpoints.sm}) {
    font-size: ${settingsTheme.typography.fontSizes.sm};
  }
`;

const Input = styled.input`
  ${commonStyles.input}
`;

const Button = styled.button`
  ${commonStyles.button.primary}

  /* Mobile - full width and larger touch targets */
  @media (max-width: ${settingsTheme.breakpoints.md}) {
    width: 100%;
    min-height: 48px;
    padding: ${settingsTheme.spacing.md} ${settingsTheme.spacing.lg};
    font-size: ${settingsTheme.typography.fontSizes.base};
    border-radius: ${settingsTheme.borderRadius.md};
  }

  /* Small mobile */
  @media (max-width: ${settingsTheme.breakpoints.sm}) {
    min-height: 44px;
    padding: ${settingsTheme.spacing.sm} ${settingsTheme.spacing.md};
    font-size: ${settingsTheme.typography.fontSizes.sm};
    border-radius: ${settingsTheme.borderRadius.sm};
  }
`;

const ErrorMessage = styled.div`
  color: ${settingsTheme.colors.error.main};
  font-size: ${settingsTheme.typography.fontSizes.sm};
  margin-top: ${settingsTheme.spacing.sm};
  padding: ${settingsTheme.spacing.sm} ${settingsTheme.spacing.md};
  background-color: ${settingsTheme.colors.error.light};
  border-radius: ${settingsTheme.borderRadius.sm};
  border-left: 3px solid ${settingsTheme.colors.error.main};
`;

const SuccessMessage = styled.div`
  color: ${settingsTheme.colors.success.main};
  font-size: ${settingsTheme.typography.fontSizes.sm};
  margin-top: ${settingsTheme.spacing.sm};
  padding: ${settingsTheme.spacing.sm} ${settingsTheme.spacing.md};
  background-color: ${settingsTheme.colors.success.light};
  border-radius: ${settingsTheme.borderRadius.sm};
  border-left: 3px solid ${settingsTheme.colors.success.main};
`;

const ProfileImageContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: ${settingsTheme.spacing.xl};
  margin-bottom: ${settingsTheme.spacing['2xl']};
  padding: ${settingsTheme.spacing['2xl']};
  background: linear-gradient(135deg, ${settingsTheme.colors.background.light} 0%, ${settingsTheme.colors.background.main} 100%);
  border-radius: ${settingsTheme.borderRadius.lg};
  border: 1px solid ${settingsTheme.colors.border};
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #6366f1, #8b5cf6, #ec4899);
  }

  /* Tablet */
  @media (max-width: ${settingsTheme.breakpoints.lg}) and (min-width: ${settingsTheme.breakpoints.md}) {
    padding: ${settingsTheme.spacing.xl};
    gap: ${settingsTheme.spacing.lg};
    margin-bottom: ${settingsTheme.spacing.xl};
    border-radius: ${settingsTheme.borderRadius.md};
  }

  /* Mobile */
  @media (max-width: ${settingsTheme.breakpoints.md}) {
    padding: ${settingsTheme.spacing.lg};
    gap: ${settingsTheme.spacing.md};
    margin-bottom: ${settingsTheme.spacing.lg};
    border-radius: ${settingsTheme.borderRadius.sm};

    &::before {
      height: 3px;
    }
  }

  /* Small mobile */
  @media (max-width: ${settingsTheme.breakpoints.sm}) {
    padding: ${settingsTheme.spacing.md};
    gap: ${settingsTheme.spacing.sm};
    margin-bottom: ${settingsTheme.spacing.md};
    border: none;
    background: ${settingsTheme.colors.background.main};
    border-radius: ${settingsTheme.borderRadius.sm};

    &::before {
      height: 2px;
    }
  }
`;

const ProfileImage = styled.div`
  position: relative;
  width: 140px;
  height: 140px;
  border-radius: 50%;
  background-color: ${settingsTheme.colors.border};
  background-size: cover;
  background-position: center;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  flex-shrink: 0;
  border: 6px solid ${settingsTheme.colors.background.main};
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12), 0 2px 8px rgba(0, 0, 0, 0.08);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15), 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  /* Large desktop */
  @media (min-width: ${settingsTheme.breakpoints.xl}) {
    width: 160px;
    height: 160px;
    border-width: 8px;
  }

  /* Desktop */
  @media (min-width: ${settingsTheme.breakpoints.lg}) and (max-width: ${settingsTheme.breakpoints.xl}) {
    width: 150px;
    height: 150px;
    border-width: 6px;
  }

  /* Tablet */
  @media (max-width: ${settingsTheme.breakpoints.lg}) and (min-width: ${settingsTheme.breakpoints.md}) {
    width: 130px;
    height: 130px;
    border-width: 5px;
  }

  /* Mobile */
  @media (max-width: ${settingsTheme.breakpoints.md}) and (min-width: ${settingsTheme.breakpoints.sm}) {
    width: 120px;
    height: 120px;
    border-width: 4px;
  }

  /* Small mobile */
  @media (max-width: ${settingsTheme.breakpoints.sm}) {
    width: 100px;
    height: 100px;
    border-width: 3px;

    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12), 0 2px 6px rgba(0, 0, 0, 0.08);
    }
  }
`;

const Avatar = styled.img`
  width: 100%;
  height: 100%;
  object-fit: cover;
`;

const ImagePlaceholder = styled.div`
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 50%, #ec4899 100%);
  color: white;
  font-size: 2.5rem;
  font-weight: 700;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 30% 30%, rgba(255, 255, 255, 0.2) 0%, transparent 50%);
    pointer-events: none;
  }

  /* Large desktop */
  @media (min-width: ${settingsTheme.breakpoints.xl}) {
    font-size: 3rem;
  }

  /* Tablet */
  @media (max-width: ${settingsTheme.breakpoints.lg}) and (min-width: ${settingsTheme.breakpoints.md}) {
    font-size: 2.25rem;
  }

  /* Mobile */
  @media (max-width: ${settingsTheme.breakpoints.md}) and (min-width: ${settingsTheme.breakpoints.sm}) {
    font-size: 2rem;
  }

  /* Small mobile */
  @media (max-width: ${settingsTheme.breakpoints.sm}) {
    font-size: 1.75rem;
  }
`;

const TwoColumnForm = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: ${settingsTheme.spacing.xl};

  /* Large desktop */
  @media (min-width: ${settingsTheme.breakpoints['2xl']}) {
    gap: ${settingsTheme.spacing['2xl']};
  }

  /* Desktop */
  @media (min-width: ${settingsTheme.breakpoints.xl}) and (max-width: ${settingsTheme.breakpoints['2xl']}) {
    gap: ${settingsTheme.spacing.xl};
  }

  /* Tablet */
  @media (max-width: ${settingsTheme.breakpoints.lg}) and (min-width: ${settingsTheme.breakpoints.md}) {
    gap: ${settingsTheme.spacing.lg};
  }

  /* Mobile and below - single column */
  @media (max-width: ${settingsTheme.breakpoints.md}) {
    grid-template-columns: 1fr;
    gap: ${settingsTheme.spacing.md};
  }

  /* Small mobile */
  @media (max-width: ${settingsTheme.breakpoints.sm}) {
    gap: ${settingsTheme.spacing.sm};
  }
`;

const ProfileImageWrapper = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: ${settingsTheme.spacing.lg};
  text-align: center;
`;

const UploadButton = styled.button`
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
  color: white;
  border: none;
  border-radius: ${settingsTheme.borderRadius.lg};
  font-size: ${settingsTheme.typography.fontSizes.sm};
  font-weight: ${settingsTheme.typography.fontWeights.semibold};
  padding: ${settingsTheme.spacing.md} ${settingsTheme.spacing.xl};
  min-width: 140px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
  }

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(99, 102, 241, 0.4);

    &::before {
      left: 100%;
    }
  }

  &:active {
    transform: translateY(0);
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;

    &:hover {
      transform: none;
      box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
    }
  }

  /* Tablet */
  @media (max-width: ${settingsTheme.breakpoints.lg}) and (min-width: ${settingsTheme.breakpoints.md}) {
    padding: ${settingsTheme.spacing.sm} ${settingsTheme.spacing.lg};
    min-width: 120px;
    font-size: ${settingsTheme.typography.fontSizes.sm};
  }

  /* Mobile */
  @media (max-width: ${settingsTheme.breakpoints.md}) {
    padding: ${settingsTheme.spacing.md} ${settingsTheme.spacing.lg};
    min-width: 100%;
    min-height: 44px; /* Touch target size */
    font-size: ${settingsTheme.typography.fontSizes.base};
    border-radius: ${settingsTheme.borderRadius.md};
  }

  /* Small mobile */
  @media (max-width: ${settingsTheme.breakpoints.sm}) {
    padding: ${settingsTheme.spacing.sm} ${settingsTheme.spacing.md};
    min-height: 40px;
    font-size: ${settingsTheme.typography.fontSizes.sm};
    border-radius: ${settingsTheme.borderRadius.sm};
  }
`;

const HiddenFileInput = styled.input`
  display: none;
`;

const UploadOverlay = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.9) 0%, rgba(139, 92, 246, 0.9) 100%);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  color: white;
  backdrop-filter: blur(4px);
  animation: fadeIn 0.3s ease-out;

  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: scale(0.9);
    }
    to {
      opacity: 1;
      transform: scale(1);
    }
  }
`;

const UploadSpinner = styled.div`
  width: 32px;
  height: 32px;
  border: 3px solid rgba(255, 255, 255, 0.2);
  border-top: 3px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: ${settingsTheme.spacing.sm};
  
  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
`;

const UploadText = styled.span`
  font-size: ${settingsTheme.typography.fontSizes.sm};
  font-weight: ${settingsTheme.typography.fontWeights.semibold};
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
`;

const HoverOverlay = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  color: white;
  opacity: 0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(2px);

  ${ProfileImage}:hover & {
    opacity: 1;
  }
`;

const CameraIcon = styled.span`
  font-size: 2rem;
  margin-bottom: ${settingsTheme.spacing.xs};
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
`;

const HoverText = styled.span`
  font-size: ${settingsTheme.typography.fontSizes.sm};
  font-weight: ${settingsTheme.typography.fontWeights.medium};
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
`;

const UploadInstructions = styled.div`
  text-align: center;
  margin: ${settingsTheme.spacing.md} 0;
`;

const UploadTitle = styled.h3`
  font-size: ${settingsTheme.typography.fontSizes.lg};
  font-weight: ${settingsTheme.typography.fontWeights.semibold};
  color: ${settingsTheme.colors.text.primary};
  margin: 0 0 ${settingsTheme.spacing.xs} 0;

  /* Mobile */
  @media (max-width: ${settingsTheme.breakpoints.md}) {
    font-size: ${settingsTheme.typography.fontSizes.base};
  }

  /* Small mobile */
  @media (max-width: ${settingsTheme.breakpoints.sm}) {
    font-size: ${settingsTheme.typography.fontSizes.sm};
  }
`;

const UploadSubtitle = styled.p`
  font-size: ${settingsTheme.typography.fontSizes.sm};
  color: ${settingsTheme.colors.text.secondary};
  margin: 0;
  line-height: 1.4;

  /* Mobile */
  @media (max-width: ${settingsTheme.breakpoints.md}) {
    font-size: ${settingsTheme.typography.fontSizes.xs};
    line-height: 1.3;
  }

  /* Small mobile */
  @media (max-width: ${settingsTheme.breakpoints.sm}) {
    display: none; /* Hide subtitle on very small screens */
  }
`;

const FileRequirements = styled.div`
  font-size: ${settingsTheme.typography.fontSizes.xs};
  color: ${settingsTheme.colors.text.secondary};
  text-align: center;
  margin-top: ${settingsTheme.spacing.sm};
  padding: ${settingsTheme.spacing.xs} ${settingsTheme.spacing.sm};
  background-color: ${settingsTheme.colors.background.light};
  border-radius: ${settingsTheme.borderRadius.sm};
  border: 1px solid ${settingsTheme.colors.border};

  /* Mobile */
  @media (max-width: ${settingsTheme.breakpoints.md}) {
    font-size: ${settingsTheme.typography.fontSizes.xs};
    padding: ${settingsTheme.spacing.xs};
    margin-top: ${settingsTheme.spacing.xs};
  }

  /* Small mobile */
  @media (max-width: ${settingsTheme.breakpoints.sm}) {
    font-size: 10px;
    padding: 2px ${settingsTheme.spacing.xs};
  }
`;

// Crop Modal Components
const CropModalOverlay = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(4px);
`;

const CropModalContainer = styled.div`
  background: ${settingsTheme.colors.background.main};
  border-radius: ${settingsTheme.borderRadius.lg};
  padding: ${settingsTheme.spacing.xl};
  width: 90vw;
  max-width: 600px;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  border: 1px solid ${settingsTheme.colors.border};

  /* Tablet */
  @media (max-width: ${settingsTheme.breakpoints.lg}) and (min-width: ${settingsTheme.breakpoints.md}) {
    width: 85vw;
    max-width: 500px;
    padding: ${settingsTheme.spacing.lg};
    border-radius: ${settingsTheme.borderRadius.md};
  }

  /* Mobile */
  @media (max-width: ${settingsTheme.breakpoints.md}) {
    width: 95vw;
    max-width: none;
    max-height: 90vh;
    padding: ${settingsTheme.spacing.md};
    border-radius: ${settingsTheme.borderRadius.sm};
    margin: ${settingsTheme.spacing.sm};
  }

  /* Small mobile */
  @media (max-width: ${settingsTheme.breakpoints.sm}) {
    width: 100vw;
    height: 100vh;
    max-height: none;
    padding: ${settingsTheme.spacing.sm};
    border-radius: 0;
    margin: 0;
  }
`;

const CropModalHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: ${settingsTheme.spacing.lg};
  padding-bottom: ${settingsTheme.spacing.md};
  border-bottom: 1px solid ${settingsTheme.colors.border};
`;

const CropModalTitle = styled.h2`
  font-size: ${settingsTheme.typography.fontSizes.xl};
  font-weight: ${settingsTheme.typography.fontWeights.semibold};
  color: ${settingsTheme.colors.text.primary};
  margin: 0;
`;

const CropModalCloseButton = styled.button`
  background: none;
  border: none;
  font-size: 1.5rem;
  color: ${settingsTheme.colors.text.secondary};
  cursor: pointer;
  padding: ${settingsTheme.spacing.xs};
  border-radius: ${settingsTheme.borderRadius.sm};
  transition: all 0.2s ease;

  &:hover {
    background-color: ${settingsTheme.colors.background.light};
    color: ${settingsTheme.colors.text.primary};
  }
`;

const CropContainer = styled.div`
  position: relative;
  width: 100%;
  height: 400px;
  background: ${settingsTheme.colors.background.lighter};
  border-radius: ${settingsTheme.borderRadius.md};
  overflow: hidden;
  margin-bottom: ${settingsTheme.spacing.lg};

  /* Tablet */
  @media (max-width: ${settingsTheme.breakpoints.lg}) and (min-width: ${settingsTheme.breakpoints.md}) {
    height: 350px;
    border-radius: ${settingsTheme.borderRadius.sm};
    margin-bottom: ${settingsTheme.spacing.md};
  }

  /* Mobile */
  @media (max-width: ${settingsTheme.breakpoints.md}) {
    height: 300px;
    border-radius: ${settingsTheme.borderRadius.sm};
    margin-bottom: ${settingsTheme.spacing.sm};
  }

  /* Small mobile */
  @media (max-width: ${settingsTheme.breakpoints.sm}) {
    height: 250px;
    border-radius: ${settingsTheme.borderRadius.sm};
    margin-bottom: ${settingsTheme.spacing.xs};
  }
`;

const CropControls = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${settingsTheme.spacing.md};
  margin-bottom: ${settingsTheme.spacing.lg};
`;

const ZoomControl = styled.div`
  display: flex;
  align-items: center;
  gap: ${settingsTheme.spacing.md};
`;

const ZoomLabel = styled.label`
  font-size: ${settingsTheme.typography.fontSizes.sm};
  font-weight: ${settingsTheme.typography.fontWeights.medium};
  color: ${settingsTheme.colors.text.primary};
  min-width: 60px;
`;

const ZoomSlider = styled.input`
  flex: 1;
  height: 6px;
  border-radius: 3px;
  background: ${settingsTheme.colors.background.light};
  outline: none;
  -webkit-appearance: none;

  &::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    cursor: pointer;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  }

  &::-moz-range-thumb {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    cursor: pointer;
    border: none;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  }
`;

const CropModalActions = styled.div`
  display: flex;
  gap: ${settingsTheme.spacing.md};
  justify-content: flex-end;

  /* Mobile */
  @media (max-width: ${settingsTheme.breakpoints.md}) {
    flex-direction: column-reverse;
    gap: ${settingsTheme.spacing.sm};
  }

  /* Small mobile */
  @media (max-width: ${settingsTheme.breakpoints.sm}) {
    gap: ${settingsTheme.spacing.xs};
  }
`;

const CropButton = styled.button<{ variant?: 'primary' | 'secondary' }>`
  padding: ${settingsTheme.spacing.sm} ${settingsTheme.spacing.lg};
  border-radius: ${settingsTheme.borderRadius.md};
  font-size: ${settingsTheme.typography.fontSizes.sm};
  font-weight: ${settingsTheme.typography.fontWeights.medium};
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid;
  min-width: 100px;

  ${props => props.variant === 'primary' ? `
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-color: transparent;

    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
    }
  ` : `
    background: ${settingsTheme.colors.background.light};
    color: ${settingsTheme.colors.text.primary};
    border-color: ${settingsTheme.colors.border};

    &:hover {
      background: ${settingsTheme.colors.background.main};
      border-color: ${settingsTheme.colors.text.secondary};
    }
  `}

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
  }

  /* Mobile */
  @media (max-width: ${settingsTheme.breakpoints.md}) {
    width: 100%;
    min-width: auto;
    min-height: 44px; /* Touch target size */
    padding: ${settingsTheme.spacing.md} ${settingsTheme.spacing.lg};
    font-size: ${settingsTheme.typography.fontSizes.base};
    border-radius: ${settingsTheme.borderRadius.sm};
  }

  /* Small mobile */
  @media (max-width: ${settingsTheme.breakpoints.sm}) {
    min-height: 40px;
    padding: ${settingsTheme.spacing.sm} ${settingsTheme.spacing.md};
    font-size: ${settingsTheme.typography.fontSizes.sm};
  }
`;



// Use the UserData interface from the store
import { UserData } from '@/store/userStore';

export default function ProfileSettings() {
  // Use the user store
  const { userData, loading, error: storeError, fetchUserData, setUserData } = useUserStore();
  const [success, setSuccess] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [saving, setSaving] = useState(false);

  // Form state
  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');
  const [phone, setPhone] = useState('');
  const [imageUrl, setImageUrl] = useState('');
  
  // Image upload state
  const [uploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const fileInputRef = useRef<HTMLInputElement>(null);
  
  // Image cropping state
  const [showCropModal, setShowCropModal] = useState(false);
  const [imageToCrop, setImageToCrop] = useState<string | null>(null);
  const [crop, setCrop] = useState<Point>({ x: 0, y: 0 });
  const [zoom, setZoom] = useState(1);
  const [croppedAreaPixels, setCroppedAreaPixels] = useState<Area | null>(null);
  const [originalFile, setOriginalFile] = useState<File | null>(null);

  const router = useRouter();

  // Fetch user data on component mount
  useEffect(() => {
    // Use the fetchUserData from the store
    fetchUserData();

    // If there's an error from the store, set it in the local state
    if (storeError) {
      setError(storeError);
    }
  }, [fetchUserData, storeError]);

  // Initialize form state when userData changes
  useEffect(() => {
    if (userData) {
      setFirstName(userData.firstName || '');
      setLastName(userData.lastName || '');
      setPhone(userData.phone || '');
      setImageUrl(userData.imageUrl || '');
    }
  }, [userData]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      setSaving(true);
      setError(null);
      setSuccess(null);

      const token = getCookie('access_token');

      if (!token) {
        setError('Authentication required. Please log in.');
        router.push('/login');
        return;
      }

      // Create update data from form fields
      const updateData: any = {
        firstName,
        lastName,
        phone: phone || null,
        imageUrl: imageUrl || null,
      };

      // We no longer handle password updates in this function
      // Password updates are handled by the handlePasswordChange function

      // First update the user data in the store for immediate UI feedback
      if (userData) {
        // Create updated user object
        const updatedUser = {
          ...userData,
          firstName: firstName,
          lastName: lastName,
          phone: phone || null,
          imageUrl: imageUrl || null,
        };

        // Update the store with the new user data
        setUserData(updatedUser);
      } else {
        throw new Error('No user data available');
      }

      // Then send the update to the backend (in a real app, we would handle failures and rollback)
      // This is done after updating the store for better user experience
      const response = await fetch('/api/v1/me', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify(updateData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update profile');
      }

      // Show success message after backend confirmation
      setSuccess('Profile updated successfully');
    } catch (err: any) {
      console.error('Error updating profile:', err);
      setError(err.message || 'Failed to update profile. Please try again.');
    } finally {
      setSaving(false);
    }
  };

  const handleImageUpload = async (file: File) => {
    try {
      setUploading(true);
      setError(null);
      setUploadProgress(0);

      // Upload to S3
      const uploadResult = await s3Service.uploadFile(file, 'avatars');

      if (uploadResult.success && uploadResult.data) {
        const newImageUrl = uploadResult.data.url;

        // Update the local image URL state immediately for UI feedback
        setImageUrl(newImageUrl);

        // Automatically save the image URL to the database
        await saveImageUrlToDatabase(newImageUrl);

        setSuccess('Profile image updated successfully');
      } else {
        throw new Error(uploadResult.error || 'Failed to upload image');
      }
    } catch (err: any) {
      console.error('Error uploading image:', err);
      setError(err.message || 'Failed to upload image. Please try again.');
    } finally {
      setUploading(false);
      setUploadProgress(0);
    }
  };

  const saveImageUrlToDatabase = async (newImageUrl: string) => {
    try {
      const token = getCookie('access_token');

      if (!token) {
        throw new Error('Authentication required. Please log in.');
      }

      // Update the user data in the store immediately for UI feedback
      if (userData) {
        const updatedUser = {
          ...userData,
          imageUrl: newImageUrl,
        };
        setUserData(updatedUser);
      }

      // Send the update to the backend
      const response = await fetch('/api/v1/me', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({ imageUrl: newImageUrl }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to save profile image');
      }

      // Optionally refresh user data from server to ensure consistency
      // await fetchUserData();

    } catch (err: any) {
      console.error('Error saving image URL to database:', err);

      // Revert the local state if database save failed
      if (userData) {
        setUserData(userData);
        setImageUrl(userData.imageUrl || '');
      }

      throw new Error(err.message || 'Failed to save profile image to database');
    }
  };

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      handleFileForCropping(file);
    }
  };

  const handleFileForCropping = async (file: File) => {
    try {
      // Validate file type
      const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
      if (!allowedTypes.includes(file.type)) {
        throw new Error('Please select a valid image file (JPEG, PNG, GIF, or WebP)');
      }

      // Validate file size (5MB for avatars)
      const maxSize = 5 * 1024 * 1024; // 5MB
      if (file.size > maxSize) {
        throw new Error('Image size must be less than 5MB');
      }

      // Create image URL for cropping
      const imageUrl = URL.createObjectURL(file);
      setImageToCrop(imageUrl);
      setOriginalFile(file);
      setShowCropModal(true);
      setError(null);
    } catch (err: any) {
      console.error('Error preparing image for cropping:', err);
      setError(err.message || 'Failed to prepare image for cropping.');
    }
  };

  const onCropComplete = useCallback((_croppedArea: Area, croppedAreaPixels: Area) => {
    setCroppedAreaPixels(croppedAreaPixels);
  }, []);

  const createCroppedImage = async (): Promise<File | null> => {
    if (!imageToCrop || !croppedAreaPixels || !originalFile) return null;

    return new Promise((resolve) => {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      const image = new Image();

      image.onload = () => {
        const { width, height, x, y } = croppedAreaPixels;
        
        canvas.width = width;
        canvas.height = height;

        ctx?.drawImage(
          image,
          x,
          y,
          width,
          height,
          0,
          0,
          width,
          height
        );

        canvas.toBlob((blob) => {
          if (blob) {
            const croppedFile = new File([blob], originalFile.name, {
              type: originalFile.type,
              lastModified: Date.now(),
            });
            resolve(croppedFile);
          } else {
            resolve(null);
          }
        }, originalFile.type, 0.9);
      };

      image.src = imageToCrop;
    });
  };

  const handleCropConfirm = async () => {
    try {
      const croppedFile = await createCroppedImage();
      if (croppedFile) {
        setShowCropModal(false);
        setImageToCrop(null);
        URL.revokeObjectURL(imageToCrop!);
        await handleImageUpload(croppedFile);
      }
    } catch (err: any) {
      console.error('Error cropping image:', err);
      setError('Failed to crop image. Please try again.');
    }
  };

  const handleCropCancel = () => {
    setShowCropModal(false);
    setImageToCrop(null);
    setCrop({ x: 0, y: 0 });
    setZoom(1);
    setCroppedAreaPixels(null);
    setOriginalFile(null);
    if (imageToCrop) {
      URL.revokeObjectURL(imageToCrop);
    }
    // Reset file input
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const triggerFileSelect = () => {
    fileInputRef.current?.click();
  };

  if (loading) {
    return (
      <ProfileContainer>
        <div className="h-8 w-48 bg-gray-200 rounded mb-6"></div>

        {/* Skeleton for profile image section */}
        <ProfileImageContainer>
          <div>
            <div className="h-3 w-24 bg-gray-200 rounded mb-2"></div>
            <div className="h-[120px] w-[120px] rounded-full bg-gray-200"></div>
          </div>
          <FormGroup style={{ flex: 1 }}>
            <div className="h-3 w-20 bg-gray-200 rounded mb-2"></div>
            <div className="h-10 w-full bg-gray-200 rounded"></div>
          </FormGroup>
        </ProfileImageContainer>

        {/* Skeleton for form fields */}
        <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '1.5rem' }}>
          <FormGroup>
            <div className="h-3 w-20 bg-gray-200 rounded mb-2"></div>
            <div className="h-10 w-full bg-gray-200 rounded"></div>
          </FormGroup>
          <FormGroup>
            <div className="h-3 w-20 bg-gray-200 rounded mb-2"></div>
            <div className="h-10 w-full bg-gray-200 rounded"></div>
          </FormGroup>
          <FormGroup>
            <div className="h-3 w-20 bg-gray-200 rounded mb-2"></div>
            <div className="h-10 w-full bg-gray-200 rounded"></div>
          </FormGroup>
          <FormGroup>
            <div className="h-3 w-20 bg-gray-200 rounded mb-2"></div>
            <div className="h-10 w-full bg-gray-200 rounded"></div>
          </FormGroup>
        </div>

        {/* Skeleton for save button */}
        <div className="h-10 w-32 bg-gray-200 rounded mt-6"></div>
      </ProfileContainer>
    );
  }

  return (
    <ProfileContainer>
      <SectionTitle>ข้อมูลส่วนตัว</SectionTitle>

      {error && <ErrorMessage>{error}</ErrorMessage>}
      {success && <SuccessMessage>{success}</SuccessMessage>}

      <form onSubmit={handleSubmit} style={{ width: '100%', maxWidth: '100%' }}>
        <ProfileImageContainer>
          <div>
            <ProfileImageWrapper>
              <ProfileImage onClick={triggerFileSelect}>
                {imageUrl ? (
                  <Avatar src={imageUrl} alt="Profile" />
                ) : (
                  <ImagePlaceholder>
                    {firstName && lastName ? `${firstName[0]}${lastName[0]}` : 'U'}
                  </ImagePlaceholder>
                )}
                {uploading && (
                  <UploadOverlay>
                    <UploadSpinner />
                    <UploadText>กำลังอัปโหลด...</UploadText>
                  </UploadOverlay>
                )}
                {!uploading && (
                  <HoverOverlay>
                    <CameraIcon>📷</CameraIcon>
                    <HoverText>คลิกเพื่ออัปโหลด</HoverText>
                  </HoverOverlay>
                )}
              </ProfileImage>
              <UploadInstructions>
                <UploadTitle>รูปภาพประจำวัน</UploadTitle>
                <UploadSubtitle>คลิกเพื่ออัปโหลด</UploadSubtitle>
              </UploadInstructions>
              <UploadButton type="button" onClick={triggerFileSelect} disabled={uploading}>
                {uploading ? 'กำลังอัปโหลด...' : '📁 เลือกไฟล์'}
              </UploadButton>
              <FileRequirements>
                JPG, PNG, GIF or WebP • Max 5MB
              </FileRequirements>
              <HiddenFileInput
                ref={fileInputRef}
                type="file"
                accept="image/jpeg,image/png,image/gif,image/webp"
                onChange={handleFileSelect}
              />
            </ProfileImageWrapper>
          </div>
        </ProfileImageContainer>

        <TwoColumnForm>
          <FormGroup>
            <Label htmlFor="firstName">ชื่อ</Label>
            <Input
              type="text"
              id="firstName"
              placeholder="ชื่อ"
              value={firstName}
              onChange={e => setFirstName(e.target.value)}
              required
            />
          </FormGroup>

          <FormGroup>
            <Label htmlFor="lastName">นามสกุล</Label>
            <Input
              type="text"
              id="lastName"
              placeholder="นามสกุล"
              value={lastName}
              onChange={e => setLastName(e.target.value)}
              required
            />
          </FormGroup>
        </TwoColumnForm>

        <FormGroup style={{ maxWidth: '100%' }}>
          <Label htmlFor="email">อีเมล</Label>
          <Input
            type="email"
            id="email"
            value={userData?.email || ''}
            disabled
            style={{ maxWidth: '100%' }}
          />
          <small style={{ color: '#6b7280', marginTop: '0.25rem' }}>อีเมลไม่สามารถเปลี่ยนแปลงได้</small>
        </FormGroup>

        <FormGroup style={{ maxWidth: '100%' }}>
          <Label htmlFor="phone">เบอร์โทรศัพท์</Label>
          <Input
            type="tel"
            id="phone"
            placeholder="เบอร์โทรศัพท์"
            value={phone || ''}
            onChange={e => setPhone(e.target.value)}
            style={{ maxWidth: '100%' }}
          />
        </FormGroup>

        <FormGroup style={{ maxWidth: '100%' }}>
          <Label htmlFor="role">ตำแหน่ง</Label>
          <Input
            type="text"
            id="role"
            value={userData?.role.name || ''}
            disabled
            style={{ maxWidth: '100%' }}
          />
        </FormGroup>

        <div style={{ width: '100%', marginTop: '1rem' }}>
          <Button type="submit" disabled={saving}>
            {saving ? 'กำลังบันทึก...' : 'บันทึก'}
          </Button>
        </div>
      </form>
      
      {/* Crop Modal */}
      {showCropModal && imageToCrop && (
        <CropModalOverlay>
          <CropModalContainer>
            <CropModalHeader>
              <CropModalTitle>ตัดรูปภาพ</CropModalTitle>
              <CropModalCloseButton onClick={handleCropCancel}>
                ×
              </CropModalCloseButton>
            </CropModalHeader>
            
            <CropContainer>
              <Cropper
                image={imageToCrop || ""}
                crop={crop}
                zoom={zoom}
                aspect={1}
                onCropChange={setCrop}
                onZoomChange={setZoom}
                onCropComplete={onCropComplete}
                cropShape="round"
                showGrid={false}
              />
            </CropContainer>
            
            <CropControls>
              <ZoomControl>
                <ZoomLabel>Zoom:</ZoomLabel>
                <ZoomSlider
                  type="range"
                  min={1}
                  max={3}
                  step={0.1}
                  value={zoom}
                  onChange={(e) => setZoom(Number(e.target.value))}
                />
              </ZoomControl>
            </CropControls>
            
            <CropModalActions>
              <CropButton variant="secondary" onClick={handleCropCancel}>
                ยกเลิก
              </CropButton>
              <CropButton variant="primary" onClick={handleCropConfirm}>
                ตัดรูปภาพ
              </CropButton>
            </CropModalActions>
          </CropModalContainer>
        </CropModalOverlay>
      )}
    </ProfileContainer>
  );
};

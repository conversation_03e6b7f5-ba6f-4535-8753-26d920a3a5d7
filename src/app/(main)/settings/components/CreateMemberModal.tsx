'use client';

import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { X } from 'lucide-react';
import { toast } from 'react-hot-toast';
import { useAuth } from '@/hooks/useAuth';
import { appTheme as settingsTheme, appStyles as commonStyles } from '@/app/theme';

// Modal components
const ModalOverlay = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
`;

const ModalContent = styled.div`
  background-color: ${settingsTheme.colors.background.main};
  border-radius: ${settingsTheme.borderRadius.lg};
  padding: ${settingsTheme.spacing['2xl']};
  width: 100%;
  max-width: 500px;
  box-shadow: ${settingsTheme.shadows.lg};
`;

const ModalHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: ${settingsTheme.spacing.xl};
`;

const ModalTitle = styled.h3`
  font-size: ${settingsTheme.typography.fontSizes.xl};
  font-weight: ${settingsTheme.typography.fontWeights.semibold};
  color: ${settingsTheme.colors.text.primary};
`;

const ModalCloseButton = styled.button`
  background: none;
  border: none;
  cursor: pointer;
  color: ${settingsTheme.colors.text.tertiary};
  display: flex;
  align-items: center;
  justify-content: center;
  padding: ${settingsTheme.spacing.xs};
  border-radius: ${settingsTheme.borderRadius.sm};
  transition: ${settingsTheme.transitions.default};

  &:hover {
    color: ${settingsTheme.colors.text.secondary};
    background-color: ${settingsTheme.colors.background.lighter};
  }
`;

const ModalBody = styled.div`
  margin-bottom: ${settingsTheme.spacing.xl};
`;

const ModalFooter = styled.div`
  display: flex;
  justify-content: flex-end;
  gap: ${settingsTheme.spacing.md};
`;

const CancelButton = styled.button`
  ${commonStyles.button.secondary}
`;

const SaveButton = styled.button`
  ${commonStyles.button.primary}
`;

const FormGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${settingsTheme.spacing.sm};
  margin-bottom: ${settingsTheme.spacing.base};
`;

const FormLabel = styled.label`
  font-size: ${settingsTheme.typography.fontSizes.sm};
  font-weight: ${settingsTheme.typography.fontWeights.medium};
  color: ${settingsTheme.colors.text.secondary};
`;

const FormInput = styled.input`
  ${commonStyles.input}
`;

const FormSelect = styled.select`
  ${commonStyles.input}
  background-color: ${settingsTheme.colors.background.light};
`;

const LoadingSpinner = styled.div`
  display: inline-block;
  width: 1rem;
  height: 1rem;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: white;
  animation: spin 1s ease-in-out infinite;
  margin-right: ${settingsTheme.spacing.sm};

  @keyframes spin {
    to {
      transform: rotate(360deg);
    }
  }
`;

interface Organization {
  id: number;
  name: string;
}

interface Department {
  id: number;
  name: string;
  organization: Organization;
}

interface LeaderDepartment {
  id: number;
  name: string;
  organizationId: number;
  organizationName: string;
}

interface CreateMemberModalProps {
  isOpen: boolean;
  onClose: () => void;
  onMemberAdded: () => Promise<void>;
  // Role-based props
  hasAdminPrivileges?: boolean;
  isDepartmentLeader?: boolean;
  leaderDepartments?: LeaderDepartment[];
}

interface NewMemberData {
  firstName: string;
  lastName: string;
  email: string;
  password: string;
  phone: string;
  departmentId: number;
  isLeader: boolean;
}

const CreateMemberModal: React.FC<CreateMemberModalProps> = ({
  isOpen,
  onClose,
  onMemberAdded,
  hasAdminPrivileges = false,
  isDepartmentLeader = false,
  leaderDepartments = [],
}) => {
  const [organizations, setOrganizations] = useState<Organization[]>([]);
  const [departments, setDepartments] = useState<Department[]>([]);
  const [selectedOrg, setSelectedOrg] = useState<Organization | null>(null);
  const [selectedDept, setSelectedDept] = useState<Department | null>(null);
  const [isLoadingOrgs, setIsLoadingOrgs] = useState(false);
  const [isLoadingDepts, setIsLoadingDepts] = useState(false);
  const [fetchError, setFetchError] = useState<string | null>(null);

  const [newMemberData, setNewMemberData] = useState<NewMemberData>({
    firstName: '',
    lastName: '',
    email: '',
    password: '',
    phone: '',
    departmentId: 0,
    isLeader: false,
  });
  const [isAddingMember, setIsAddingMember] = useState(false);
  const [addMemberError, setAddMemberError] = useState<string | null>(null);
  const { getToken } = useAuth();

  // Fetch organizations on component mount (only for admin/owner users)
  useEffect(() => {
    if (isOpen) {
      if (hasAdminPrivileges) {
        fetchOrganizations();
      } else if (isDepartmentLeader) {
        // For department leaders, set up departments directly from props
        setupDepartmentLeaderData();
      }
    }
  }, [isOpen, hasAdminPrivileges, isDepartmentLeader]);

  // Fetch departments when organization is selected (only for admin/owner users)
  useEffect(() => {
    if (hasAdminPrivileges && selectedOrg) {
      fetchDepartments(selectedOrg.id);
    } else if (!hasAdminPrivileges) {
      // For department leaders, departments are already set up
      return;
    } else {
      setDepartments([]);
      setSelectedDept(null);
    }
  }, [selectedOrg, hasAdminPrivileges]);

  // Update departmentId when department is selected
  useEffect(() => {
    if (selectedDept) {
      setNewMemberData(prev => ({
        ...prev,
        departmentId: selectedDept.id,
      }));
    } else {
      setNewMemberData(prev => ({
        ...prev,
        departmentId: 0,
      }));
    }
  }, [selectedDept]);

  const fetchOrganizations = async () => {
    setIsLoadingOrgs(true);
    setFetchError(null);

    try {
      const token = await getToken();
      if (!token) {
        setFetchError('Authentication required');
        setIsLoadingOrgs(false);
        return;
      }

      const response = await fetch('/api/v1/organization', {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        throw new Error('Failed to fetch organizations');
      }

      const data = await response.json();
      const orgs = data.organizations || [];
      setOrganizations(orgs);

      // Set the first organization as default if available
      if (orgs.length > 0) {
        setSelectedOrg(orgs[0]);
      }
    } catch (err) {
      setFetchError('Error fetching organizations');
      console.error(err);
    } finally {
      setIsLoadingOrgs(false);
    }
  };

  const fetchDepartments = async (organizationId: number) => {
    setIsLoadingDepts(true);
    setFetchError(null);

    try {
      const token = await getToken();
      if (!token) {
        setFetchError('Authentication required');
        setIsLoadingDepts(false);
        return;
      }

      const response = await fetch(`/api/v1/department?organizationId=${organizationId}`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        throw new Error('Failed to fetch departments');
      }

      const data = await response.json();
      setDepartments(data.departments || []);
    } catch (err) {
      setFetchError('Error fetching departments');
      console.error(err);
    } finally {
      setIsLoadingDepts(false);
    }
  };

  const setupDepartmentLeaderData = () => {
    // For department leaders, convert their leader departments to the format expected by the modal
    const leaderDepts: Department[] = leaderDepartments.map(dept => ({
      id: dept.id,
      name: dept.name,
      organization: {
        id: dept.organizationId,
        name: dept.organizationName,
      },
    }));

    setDepartments(leaderDepts);

    // Auto-select the first department if available
    if (leaderDepts.length > 0) {
      setSelectedDept(leaderDepts[0]);
    }
  };

  const handleOrgChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const orgId = parseInt(e.target.value);
    const org = organizations.find(o => o.id === orgId) || null;
    setSelectedOrg(org);
    setSelectedDept(null);
  };

  const handleDeptChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const deptId = parseInt(e.target.value);
    const dept = departments.find(d => d.id === deptId) || null;
    setSelectedDept(dept);
  };

  const handleCloseModal = () => {
    setNewMemberData({
      firstName: '',
      lastName: '',
      email: '',
      password: '',
      phone: '',
      departmentId: 0,
      isLeader: false,
    });
    setSelectedOrg(null);
    setSelectedDept(null);
    setAddMemberError(null);
    onClose();
  };

  const handleAddMember = async () => {
    // Validate form data
    if (
      !newMemberData.firstName ||
      !newMemberData.lastName ||
      !newMemberData.email ||
      !newMemberData.password ||
      !newMemberData.departmentId ||
      !selectedDept
    ) {
      const errorMessage = isDepartmentLeader
        ? 'Please fill in all required fields including department'
        : 'Please fill in all required fields including organization and department';
      setAddMemberError(errorMessage);
      return;
    }

    // Additional validation for department leaders
    if (isDepartmentLeader) {
      const canAddToDept = leaderDepartments.some(dept => dept.id === selectedDept.id);
      if (!canAddToDept) {
        setAddMemberError('You can only add members to departments you lead');
        return;
      }
    }

    setIsAddingMember(true);
    setAddMemberError(null);

    try {
      const token = await getToken();
      if (!token) {
        setAddMemberError('Authentication required');
        setIsAddingMember(false);
        return;
      }

      const response = await fetch('/api/v1/member', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({
          departmentId: newMemberData.departmentId,
          firstName: newMemberData.firstName,
          lastName: newMemberData.lastName,
          email: newMemberData.email,
          password: newMemberData.password,
          phone: newMemberData.phone || undefined,
          isLeader: newMemberData.isLeader,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to add member');
      }

      await response.json();

      // Show success message
      toast.success('Member added successfully');

      // Re-fetch members data to ensure we have the latest data
      await onMemberAdded();

      // Close the modal
      handleCloseModal();
    } catch (err: any) {
      setAddMemberError(err.message || 'Error adding member');
      console.error(err);
    } finally {
      setIsAddingMember(false);
    }
  };

  if (!isOpen) return null;

  return (
    <ModalOverlay>
      <ModalContent>
        <ModalHeader>
          <ModalTitle>เพิ่มสมาชิกใหม่</ModalTitle>
          <ModalCloseButton onClick={handleCloseModal}>
            <X size={18} />
          </ModalCloseButton>
        </ModalHeader>
        <ModalBody>
          <FormGroup>
            <FormLabel>ชื่อ *</FormLabel>
            <FormInput
              type="text"
              value={newMemberData.firstName}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                setNewMemberData({ ...newMemberData, firstName: e.target.value })
              }
              placeholder="ชื่อ"
              required
            />
          </FormGroup>

          <FormGroup>
            <FormLabel>นามสกุล *</FormLabel>
            <FormInput
              type="text"
              value={newMemberData.lastName}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                setNewMemberData({ ...newMemberData, lastName: e.target.value })
              }
              placeholder="นามสกุล"
              required
            />
          </FormGroup>

          <FormGroup>
            <FormLabel>Email *</FormLabel>
            <FormInput
              type="email"
              value={newMemberData.email}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                setNewMemberData({ ...newMemberData, email: e.target.value })
              }
              placeholder="อีเมล"
              required
            />
          </FormGroup>

          <FormGroup>
            <FormLabel>Password *</FormLabel>
            <FormInput
              type="password"
              value={newMemberData.password}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                setNewMemberData({ ...newMemberData, password: e.target.value })
              }
              placeholder="รหัสผ่าน"
              required
            />
          </FormGroup>

          <FormGroup>
            <FormLabel>Phone (Optional)</FormLabel>
            <FormInput
              type="tel"
              value={newMemberData.phone}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                setNewMemberData({ ...newMemberData, phone: e.target.value })
              }
              placeholder="เบอร์โทรศัพท์"
            />
          </FormGroup>

          {/* Show organization field only for admin/owner users */}
          {hasAdminPrivileges && (
            <FormGroup>
              <FormLabel>สังกัด *</FormLabel>
              {isLoadingOrgs ? (
                <div>กำลังโหลดสังกัด...</div>
              ) : (
                <FormSelect value={selectedOrg?.id || ''} onChange={handleOrgChange} required>
                  <option value="">เลือกสังกัด</option>
                  {organizations.map(org => (
                    <option key={org.id} value={org.id}>
                      {org.name}
                    </option>
                  ))}
                </FormSelect>
              )}
            </FormGroup>
          )}

          <FormGroup>
            <FormLabel>แผนก *</FormLabel>
            {isLoadingDepts ? (
              <div>กำลังโหลดแผนก...</div>
            ) : (
              <FormSelect
                value={selectedDept?.id || ''}
                onChange={handleDeptChange}
                disabled={
                  hasAdminPrivileges
                    ? (!selectedOrg || departments.length === 0)
                    : (departments.length === 0)
                }
                required
              >
                <option value="">
                  {isDepartmentLeader
                    ? 'เลือกแผนก'
                    : 'เลือกแผนก'
                  }
                </option>
                {departments.map(dept => (
                  <option key={dept.id} value={dept.id}>
                    {isDepartmentLeader
                      ? `${dept.name} (${dept.organization.name})`
                      : dept.name
                    }
                  </option>
                ))}
              </FormSelect>
            )}
          </FormGroup>

          <FormGroup>
            <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
              <input
                type="checkbox"
                id="isLeader"
                checked={newMemberData.isLeader}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                  setNewMemberData({ ...newMemberData, isLeader: e.target.checked })
                }
              />
              <FormLabel htmlFor="isLeader" style={{ margin: 0, cursor: 'pointer' }}>
                ผู้นำแผนก
              </FormLabel>
            </div>
          </FormGroup>

          {(addMemberError || fetchError) && (
            <div style={{ color: '#ef4444', marginTop: '0.5rem', fontSize: '0.875rem' }}>
              {addMemberError || fetchError}
            </div>
          )}
        </ModalBody>
        <ModalFooter>
          <CancelButton onClick={handleCloseModal} disabled={isAddingMember}>
            ยกเลิก
          </CancelButton>
          <SaveButton
            onClick={handleAddMember}
            disabled={
              isAddingMember ||
              !selectedDept ||
              (hasAdminPrivileges && !selectedOrg)
            }
          >
            {isAddingMember ? <LoadingSpinner /> : null}
            เพิ่มสมาชิก
          </SaveButton>
        </ModalFooter>
      </ModalContent>
    </ModalOverlay>
  );
};

export default CreateMemberModal;

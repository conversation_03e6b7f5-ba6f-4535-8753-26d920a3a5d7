'use client';

import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { useRouter } from 'next/navigation';
import { getCookie } from 'cookies-next';
import { appTheme as settingsTheme, appStyles as commonStyles } from '@/app/theme';

const PointHistoryContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${settingsTheme.spacing.base};
  width: 100%;
  padding: ${settingsTheme.spacing.xl};
  background-color: ${settingsTheme.colors.background.main};
  border-radius: ${settingsTheme.borderRadius.lg};
  box-shadow: ${settingsTheme.shadows.md};

  @media (max-width: ${settingsTheme.breakpoints.md}) {
    padding: ${settingsTheme.spacing.base};
    gap: ${settingsTheme.spacing.base};
  }
`;

const SectionTitle = styled.h2`
  ${commonStyles.sectionTitle}
`;

const PointSummary = styled.div`
  display: flex;
  align-items: center;
  gap: ${settingsTheme.spacing.xl};
  padding: ${settingsTheme.spacing.lg};
  background-color: ${settingsTheme.colors.background.light};
  border-radius: ${settingsTheme.borderRadius.md};
  margin-bottom: ${settingsTheme.spacing.sm};
  @media (max-width: ${settingsTheme.breakpoints.sm}) {
    flex-direction: column;
    align-items: flex-start;
    gap: ${settingsTheme.spacing.md};
  }
`;

const PointCard = styled.div`
  display: flex;
  flex-direction: column;
  padding: ${settingsTheme.spacing.lg};
  background-color: ${settingsTheme.colors.background.main};
  border-radius: ${settingsTheme.borderRadius.md};
  box-shadow: ${settingsTheme.shadows.sm};
  min-width: 200px;

  h3 {
    font-size: ${settingsTheme.typography.fontSizes.base};
    color: ${settingsTheme.colors.text.secondary};
    margin: 0 0 ${settingsTheme.spacing.sm} 0;
  }

  p {
    font-size: ${settingsTheme.typography.fontSizes['2xl']};
    font-weight: ${settingsTheme.typography.fontWeights.bold};
    color: ${settingsTheme.colors.text.primary};
    margin: 0;
  }
`;

const HistoryTable = styled.div`
  width: 100%;
  border-radius: ${settingsTheme.borderRadius.md};
  overflow: hidden;
  box-shadow: ${settingsTheme.shadows.sm};
`;

const TableHeader = styled.div`
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr 1fr;
  padding: ${settingsTheme.spacing.base};
  background-color: ${settingsTheme.colors.background.light};
  border-bottom: 1px solid ${settingsTheme.colors.border};
  font-weight: ${settingsTheme.typography.fontWeights.medium};

  @media (max-width: ${settingsTheme.breakpoints.md}) {
    grid-template-columns: 2fr 1fr 1fr;
    & > *:nth-child(4),
    & > *:nth-child(5) {
      display: none;
    }
  }
`;

const TableRow = styled.div`
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr 1fr;
  padding: ${settingsTheme.spacing.base};
  border-bottom: 1px solid ${settingsTheme.colors.border};
  background-color: ${settingsTheme.colors.background.main};

  &:nth-child(even) {
    background-color: ${settingsTheme.colors.background.light};
  }

  @media (max-width: ${settingsTheme.breakpoints.md}) {
    grid-template-columns: 2fr 1fr 1fr;
    & > *:nth-child(4),
    & > *:nth-child(5) {
      display: none;
    }
  }
`;

const TableCell = styled.div`
  display: flex;
  align-items: center;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
`;

const StatusBadge = styled.span<{ color?: string }>`
  padding: ${settingsTheme.spacing.xs} ${settingsTheme.spacing.sm};
  border-radius: ${settingsTheme.borderRadius.sm};
  font-size: ${settingsTheme.typography.fontSizes.xs};
  font-weight: ${settingsTheme.typography.fontWeights.medium};
  background-color: ${props => props.color || '#4a6cf7'}; /* Default blue color */
  color: white;
  display: inline-flex;
  align-items: center;
  justify-content: center;
`;

const PointBadge = styled.span<{ type: 'earned' | 'spent' }>`
  padding: ${settingsTheme.spacing.xs} ${settingsTheme.spacing.sm};
  border-radius: ${settingsTheme.borderRadius.sm};
  font-size: ${settingsTheme.typography.fontSizes.xs};
  font-weight: ${settingsTheme.typography.fontWeights.medium};
  background-color: ${props =>
    props.type === 'earned'
      ? settingsTheme.colors.success.light
      : settingsTheme.colors.error.light};
  color: ${props =>
    props.type === 'earned' ? settingsTheme.colors.success.main : settingsTheme.colors.error.main};
  display: inline-flex;
  align-items: center;
  justify-content: center;
`;

const EmptyState = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: ${settingsTheme.spacing.xl};
  text-align: center;

  h3 {
    font-size: ${settingsTheme.typography.fontSizes.lg};
    margin-bottom: ${settingsTheme.spacing.md};
  }

  p {
    color: ${settingsTheme.colors.text.secondary};
  }
`;

const SkeletonPulse = styled.div`
  display: inline-block;
  height: 100%;
  width: 100%;
  background: linear-gradient(
    90deg,
    ${settingsTheme.colors.background.light} 25%,
    ${settingsTheme.colors.background.main} 50%,
    ${settingsTheme.colors.background.light} 75%
  );
  background-size: 200% 100%;
  animation: pulse 1.5s ease-in-out infinite;
  border-radius: ${settingsTheme.borderRadius.sm};

  @keyframes pulse {
    0% {
      background-position: 200% 0;
    }
    100% {
      background-position: -200% 0;
    }
  }
`;

const SkeletonCard = styled.div`
  height: 80px;
  width: 100%;
  border-radius: ${settingsTheme.borderRadius.md};
  overflow: hidden;
`;

interface SkeletonTextProps {
  width?: string;
  mb?: string;
}

const SkeletonText = styled.div<SkeletonTextProps>`
  height: 20px;
  width: ${props => props.width || '100%'};
  margin-bottom: ${props => props.mb || '0'};
  border-radius: ${settingsTheme.borderRadius.sm};
  overflow: hidden;
`;

const SkeletonTableRow = styled.div`
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr 1fr;
  gap: ${settingsTheme.spacing.base};
  padding: ${settingsTheme.spacing.base};
  border-bottom: 1px solid ${settingsTheme.colors.border};

  @media (max-width: ${settingsTheme.breakpoints.md}) {
    grid-template-columns: 2fr 1fr 1fr;
    & > *:nth-child(4),
    & > *:nth-child(5) {
      display: none;
    }
  }
`;

interface TaskStatus {
  id: number;
  name: string;
  displayName: string;
  color: string;
  description: string;
  index: number;
  isMemberDisplay: boolean;
  createdAt: string;
  updatedAt: string;
}

interface PointTransaction {
  id: number;
  userId: number;
  taskId: number | null;
  pointAmount: number;
  createdAt: string;
  updatedAt: string;
  task?: {
    id: number;
    taskTitle: string;
    taskDescription: string;
    status: TaskStatus;
    createdByUser: {
      id: number;
      firstName: string;
      lastName: string;
      email: string;
      imageUrl: string | null;
    };
    assignedToUser: {
      id: number;
      firstName: string;
      lastName: string;
      email: string;
      imageUrl: string | null;
    } | null;
  } | null;
}

export default function PointHistory() {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [pointBalance, setPointBalance] = useState(0);
  const [transactions, setTransactions] = useState<PointTransaction[]>([]);

  const router = useRouter();

  useEffect(() => {
    const fetchPointHistory = async () => {
      try {
        setLoading(true);
        setError(null);

        const token = getCookie('access_token');

        if (!token) {
          setError('Authentication required. Please log in.');
          router.push('/login');
          return;
        }

        // Fetch point transactions from the API
        const response = await fetch('/api/v1/point-transaction', {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to fetch point history');
        }

        const data = await response.json();

        setPointBalance(data.totalPoints);
        setTransactions(data.pointTransactions);
        setLoading(false);
      } catch (err: any) {
        console.error('Error fetching point history:', err);
        setError(err.message || 'Failed to fetch point history. Please try again.');
        setLoading(false);
      }
    };

    fetchPointHistory();
  }, [router]);

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    }).format(date);
  };

  const getTransactionDescription = (transaction: PointTransaction) => {
    if (transaction.task) {
      return transaction.task.taskTitle;
    }
    return transaction.pointAmount > 0 ? 'Points earned' : 'Points spent';
  };

  const getTaskStatus = (transaction: PointTransaction) => {
    if (!transaction.task?.status) return 'N/A';
    return transaction.task.status.displayName || transaction.task.status.name || 'N/A';
  };

  const getTaskAssignee = (transaction: PointTransaction) => {
    if (!transaction.task?.assignedToUser) return 'Unassigned';
    const user = transaction.task.assignedToUser;
    return `${user.firstName} ${user.lastName}`;
  };

  const getTransactionType = (transaction: PointTransaction) => {
    return transaction.pointAmount > 0 ? 'earned' : 'spent';
  };

  if (loading) {
    return (
      <PointHistoryContainer>
        <SectionTitle>Point History</SectionTitle>

        {/* Skeleton for Point Summary */}
        <PointSummary>
          <PointCard>
            <SkeletonText width="60%" mb="8px">
              <SkeletonPulse />
            </SkeletonText>
            <SkeletonText width="80%">
              <SkeletonPulse />
            </SkeletonText>
          </PointCard>
        </PointSummary>

        {/* Skeleton for Table Header */}
        <HistoryTable>
          <TableHeader>
            <TableCell>Task</TableCell>
            <TableCell>Date</TableCell>
            <TableCell>Points</TableCell>
            <TableCell>Status</TableCell>
            <TableCell>Assignee</TableCell>
          </TableHeader>

          {/* Skeleton Rows */}
          {[...Array(5)].map((_, index) => (
            <SkeletonTableRow key={index}>
              <SkeletonText>
                <SkeletonPulse />
              </SkeletonText>
              <SkeletonText>
                <SkeletonPulse />
              </SkeletonText>
              <SkeletonText>
                <SkeletonPulse />
              </SkeletonText>
              <SkeletonText>
                <SkeletonPulse />
              </SkeletonText>
              <SkeletonText>
                <SkeletonPulse />
              </SkeletonText>
            </SkeletonTableRow>
          ))}
        </HistoryTable>
      </PointHistoryContainer>
    );
  }

  if (error) {
    return (
      <PointHistoryContainer>
        <SectionTitle>ประวัติการคะแนน</SectionTitle>
        <div className="bg-red-50 border-l-4 border-red-400 p-4 mb-4">
          <p className="text-red-700">{error}</p>
        </div>
      </PointHistoryContainer>
    );
  }

  return (
    <PointHistoryContainer>
      <SectionTitle>ประวัติการคะแนน</SectionTitle>
      <PointSummary>
        <PointCard>
          <h3>คะแนนปัจจุบัน</h3>
          <p>{pointBalance} pts</p>
        </PointCard>
      </PointSummary>

      {transactions.length > 0 ? (
        <HistoryTable>
          <TableHeader>
            <TableCell>Task</TableCell>
            <TableCell>Date</TableCell>
            <TableCell>Points</TableCell>
            <TableCell>Status</TableCell>
            <TableCell>Assignee</TableCell>
          </TableHeader>

          {transactions.map(transaction => (
            <TableRow key={transaction.id}>
              <TableCell title={getTransactionDescription(transaction)}>
                {getTransactionDescription(transaction)}
              </TableCell>
              <TableCell>{formatDate(transaction.createdAt)}</TableCell>
              <TableCell>
                <PointBadge type={getTransactionType(transaction)}>
                  {Math.abs(transaction.pointAmount)}
                </PointBadge>
              </TableCell>
              <TableCell>
                <StatusBadge color={transaction.task?.status?.color}>
                  {getTaskStatus(transaction)}
                </StatusBadge>
              </TableCell>
              <TableCell>{getTaskAssignee(transaction)}</TableCell>
            </TableRow>
          ))}
        </HistoryTable>
      ) : (
        <EmptyState>
          <h3>ไม่มีประวัติการเปลี่ยนแปลงแต้ม</h3>
          <p>แต้มของคุณจะปรากฏที่นี่เมื่อคุณเริ่มสะสมแต้มหรือใช้แต้ม</p>
        </EmptyState>
      )}
    </PointHistoryContainer>
  );
}

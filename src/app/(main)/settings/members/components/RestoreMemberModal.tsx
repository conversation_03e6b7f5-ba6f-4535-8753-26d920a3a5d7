'use client';

import React, { useState } from 'react';
import styled from 'styled-components';
import { X, CheckCircle } from 'lucide-react';
import { useAuth } from '@/hooks/useAuth';
import useUserStore from '@/store/userStore';
import { toast } from 'react-hot-toast';
import { appTheme as settingsTheme, appStyles as commonStyles } from '@/app/theme';

interface Member {
  id: number;
  user: {
    id: number;
    firstName: string;
    lastName: string;
    email: string;
    imageUrl?: string;
    userRoleId: number;
    deletedAt?: string;
  };
}

interface RestoreMemberModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  member: Member;
}

const ModalOverlay = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
`;

const ModalContent = styled.div`
  background-color: ${settingsTheme.colors.background.main};
  border-radius: ${settingsTheme.borderRadius.lg};
  width: 100%;
  max-width: 500px;
  box-shadow: ${settingsTheme.shadows.lg};
  overflow: hidden;
`;

const ModalHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: ${settingsTheme.spacing.base} ${settingsTheme.spacing.xl};
  border-bottom: 1px solid ${settingsTheme.colors.border};
`;

const ModalTitle = styled.h3`
  margin: 0;
  font-size: ${settingsTheme.typography.fontSizes.xl};
  font-weight: ${settingsTheme.typography.fontWeights.semibold};
  color: ${settingsTheme.colors.success.main};
  display: flex;
  align-items: center;
  gap: ${settingsTheme.spacing.sm};
`;

const CloseButton = styled.button`
  background: none;
  border: none;
  cursor: pointer;
  color: ${settingsTheme.colors.text.tertiary};
  display: flex;
  align-items: center;
  justify-content: center;
  padding: ${settingsTheme.spacing.xs};
  border-radius: ${settingsTheme.borderRadius.sm};
  transition: ${settingsTheme.transitions.default};

  &:hover {
    color: ${settingsTheme.colors.text.secondary};
    background-color: ${settingsTheme.colors.background.lighter};
  }
`;

const ModalBody = styled.div`
  padding: ${settingsTheme.spacing.xl};
`;

const InfoText = styled.p`
  margin: 0 0 ${settingsTheme.spacing.base};
  color: ${settingsTheme.colors.text.primary};
  line-height: 1.5;
`;

const MemberPreview = styled.div`
  background-color: ${settingsTheme.colors.background.light};
  border-radius: ${settingsTheme.borderRadius.md};
  padding: ${settingsTheme.spacing.base};
  margin: ${settingsTheme.spacing.base} 0;
  border-left: 3px solid ${settingsTheme.colors.success.main};
`;

const MemberDetails = styled.div`
  display: flex;
  align-items: center;
  gap: ${settingsTheme.spacing.base};
`;

const MemberAvatar = styled.div`
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background-color: ${settingsTheme.colors.background.lighter};
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: ${settingsTheme.typography.fontWeights.semibold};
  color: ${settingsTheme.colors.text.secondary};
  overflow: hidden;
`;

const MemberInfo = styled.div`
  flex: 1;
`;

const MemberName = styled.div`
  font-weight: ${settingsTheme.typography.fontWeights.semibold};
  color: ${settingsTheme.colors.text.primary};
  margin-bottom: ${settingsTheme.spacing.xs};
`;

const MemberEmail = styled.div`
  color: ${settingsTheme.colors.text.secondary};
  font-size: ${settingsTheme.typography.fontSizes.sm};
`;

const DeletedStatus = styled.div`
  display: inline-block;
  padding: 0.15rem 0.5rem;
  background-color: ${settingsTheme.colors.error.light};
  color: ${settingsTheme.colors.error.main};
  border-radius: 12px;
  font-size: ${settingsTheme.typography.fontSizes.xs};
  font-weight: ${settingsTheme.typography.fontWeights.medium};
  margin-top: ${settingsTheme.spacing.sm};
`;

const ModalFooter = styled.div`
  display: flex;
  justify-content: flex-end;
  gap: ${settingsTheme.spacing.base};
  padding: ${settingsTheme.spacing.base} ${settingsTheme.spacing.xl};
  border-top: 1px solid ${settingsTheme.colors.border};
`;

const Button = styled.button`
  padding: ${settingsTheme.spacing.sm} ${settingsTheme.spacing.base};
  border-radius: ${settingsTheme.borderRadius.md};
  font-size: ${settingsTheme.typography.fontSizes.sm};
  font-weight: ${settingsTheme.typography.fontWeights.medium};
  cursor: pointer;
  transition: ${settingsTheme.transitions.default};
`;

const CancelButton = styled(Button)`
  ${commonStyles.button.secondary}
`;

const RestoreButton = styled(Button)`
  background-color: ${settingsTheme.colors.success.main};
  border: none;
  color: white;

  &:hover {
    background-color: #388e3c;
    transform: translateY(-1px);
    box-shadow: 0 4px 6px rgba(76, 175, 80, 0.25);
  }

  &:active {
    transform: translateY(0);
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
  }
`;

// RestoreMemberModal component for confirming member restoration
export default function RestoreMemberModal({
  isOpen,
  onClose,
  onSuccess,
  member,
}: RestoreMemberModalProps) {
  const [isRestoring, setIsRestoring] = useState(false);
  const { getToken } = useAuth();
  const { userData } = useUserStore();

  if (!isOpen) return null;

  const handleRestore = async () => {
    try {
      setIsRestoring(true);
      const token = await getToken();

      // Find the user ID associated with this member
      if (!member || !member.user) {
        toast.error('Member not found');
        return;
      }
      const userId = member.user.id;

      // Call the API to restore the user
      const response = await fetch(`/api/v1/user/restore?id=${userId}`, {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'ไม่สามารถกู้คืนสมาชิกได้');
      }

      toast.success('สมาชิกถูกกู้คืนเรียบร้อย');
      onSuccess();
      onClose();
    } catch (err) {
      console.error('Error restoring member:', err);
      toast.error(err instanceof Error ? err.message : 'ไม่สามารถกู้คืนสมาชิกได้');
    } finally {
      setIsRestoring(false);
    }
  };

  // Format date for display
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  // Get initials for avatar
  const getInitials = (firstName: string, lastName: string) => {
    return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase();
  };

  return (
    <ModalOverlay onClick={onClose}>
      <ModalContent onClick={e => e.stopPropagation()}>
        <ModalHeader>
          <ModalTitle>
            <CheckCircle size={20} />
            กู้คืนสมาชิก
          </ModalTitle>
          <CloseButton onClick={onClose}>
            <X size={20} />
          </CloseButton>
        </ModalHeader>
        <ModalBody>
          <InfoText>
            คุณต้องการกู้คืนสมาชิกนี้ใช่หรือไม่? พวกเขาจะได้รับการเข้าถึงระบบอีกครั้ง
          </InfoText>

          <MemberPreview>
            <MemberDetails>
              <MemberAvatar>
                {member.user.imageUrl ? (
                  <img
                    src={member.user.imageUrl}
                    alt={`${member.user.firstName} ${member.user.lastName}`}
                    style={{ width: '100%', height: '100%', objectFit: 'cover' }}
                  />
                ) : (
                  getInitials(member.user.firstName, member.user.lastName)
                )}
              </MemberAvatar>
              <MemberInfo>
                <MemberName>
                  {member.user.firstName} {member.user.lastName}
                </MemberName>
                <MemberEmail>{member.user.email}</MemberEmail>
                {member.user.deletedAt && (
                  <DeletedStatus>ถูกลบเมื่อ {formatDate(member.user.deletedAt)}</DeletedStatus>
                )}
              </MemberInfo>
            </MemberDetails>
          </MemberPreview>
        </ModalBody>
        <ModalFooter>
          <CancelButton type="button" onClick={onClose}>
            ยกเลิก
          </CancelButton>
          <RestoreButton onClick={handleRestore} disabled={isRestoring}>
            {isRestoring ? 'กำลังกู้คืน...' : 'กู้คืนสมาชิก'}
          </RestoreButton>
        </ModalFooter>
      </ModalContent>
    </ModalOverlay>
  );
}

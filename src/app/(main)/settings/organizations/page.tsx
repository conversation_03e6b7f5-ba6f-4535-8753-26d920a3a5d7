'use client';

import React, { useState, useEffect, useRef, useCallback } from 'react';
import styled from 'styled-components';
import Link from 'next/link';
import SettingsLayout from '../components/SettingsLayout';
import { Plus, Building2, ArrowRight, Loader2, Edit2 } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { getCookie } from 'cookies-next';
import { appTheme as settingsTheme, appStyles as commonStyles } from '@/app/theme';
import { s3Service } from '@/services/s3Service';
import Cropper, { Area, Point } from 'react-easy-crop';
const OrganizationsContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${settingsTheme.spacing.xl};
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: ${settingsTheme.spacing.xl};
  background-color: ${settingsTheme.colors.background.main};
  border-radius: ${settingsTheme.borderRadius.lg};
  box-shadow: ${settingsTheme.shadows.md};

  /* Large desktop */
  @media (min-width: ${settingsTheme.breakpoints['2xl']}) {
    max-width: 1400px;
    padding: ${settingsTheme.spacing['2xl']};
    gap: ${settingsTheme.spacing['2xl']};
  }

  /* Tablet */
  @media (max-width: ${settingsTheme.breakpoints.lg}) and (min-width: ${settingsTheme.breakpoints.md}) {
    max-width: 100%;
    padding: ${settingsTheme.spacing.lg};
    gap: ${settingsTheme.spacing.lg};
    border-radius: ${settingsTheme.borderRadius.md};
  }

  /* Mobile */
  @media (max-width: ${settingsTheme.breakpoints.md}) {
    padding: ${settingsTheme.spacing.md};
    gap: ${settingsTheme.spacing.md};
    border-radius: ${settingsTheme.borderRadius.sm};
    box-shadow: ${settingsTheme.shadows.sm};
  }

  /* Small mobile */
  @media (max-width: ${settingsTheme.breakpoints.sm}) {
    padding: ${settingsTheme.spacing.sm};
    gap: ${settingsTheme.spacing.sm};
    border-radius: 0;
    box-shadow: none;
    background-color: transparent;
  }
`;

const Header = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: ${settingsTheme.spacing.lg};
  flex-wrap: wrap;
  gap: ${settingsTheme.spacing.md};

  /* Mobile */
  @media (max-width: ${settingsTheme.breakpoints.md}) {
    flex-direction: column;
    align-items: stretch;
    gap: ${settingsTheme.spacing.sm};
    margin-bottom: ${settingsTheme.spacing.md};
  }

  /* Small mobile */
  @media (max-width: ${settingsTheme.breakpoints.sm}) {
    margin-bottom: ${settingsTheme.spacing.sm};
  }
`;

const Title = styled.h2`
  ${commonStyles.sectionTitle}
`;

const AddButton = styled.button`
  ${commonStyles.button.primary}
  display: flex;
  align-items: center;
  gap: ${settingsTheme.spacing.sm};

  /* Mobile */
  @media (max-width: ${settingsTheme.breakpoints.md}) {
    width: 100%;
    justify-content: center;
    min-height: 44px;
    padding: ${settingsTheme.spacing.md} ${settingsTheme.spacing.lg};
    font-size: ${settingsTheme.typography.fontSizes.base};
  }

  /* Small mobile */
  @media (max-width: ${settingsTheme.breakpoints.sm}) {
    min-height: 40px;
    padding: ${settingsTheme.spacing.sm} ${settingsTheme.spacing.md};
    font-size: ${settingsTheme.typography.fontSizes.sm};
  }
`;

const OrganizationGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: ${settingsTheme.spacing.lg};

  /* Large desktop */
  @media (min-width: ${settingsTheme.breakpoints['2xl']}) {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: ${settingsTheme.spacing['2xl']};
  }

  /* Desktop */
  @media (min-width: ${settingsTheme.breakpoints.xl}) and (max-width: ${settingsTheme.breakpoints['2xl']}) {
    grid-template-columns: repeat(auto-fill, minmax(260px, 1fr));
    gap: ${settingsTheme.spacing.xl};
  }

  /* Tablet */
  @media (max-width: ${settingsTheme.breakpoints.lg}) and (min-width: ${settingsTheme.breakpoints.md}) {
    grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
    gap: ${settingsTheme.spacing.md};
  }

  /* Mobile */
  @media (max-width: ${settingsTheme.breakpoints.md}) {
    grid-template-columns: 1fr;
    gap: ${settingsTheme.spacing.sm};
  }

  /* Small mobile */
  @media (max-width: ${settingsTheme.breakpoints.sm}) {
    gap: ${settingsTheme.spacing.xs};
  }
`;

const OrganizationCard = styled.div`
  ${commonStyles.card}
  border: 1px solid ${settingsTheme.colors.border};
  transition: ${settingsTheme.transitions.default};

  &:hover {
    box-shadow: ${settingsTheme.shadows.md};
    border-color: ${settingsTheme.colors.primary};
    transform: translateY(-2px);
  }
`;

const OrgIcon = styled.div`
  width: 48px;
  height: 48px;
  border-radius: ${settingsTheme.borderRadius.md};
  background-color: ${settingsTheme.colors.primaryLight};
  color: ${settingsTheme.colors.primary};
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: ${settingsTheme.spacing.lg};
`;

const OrgImage = styled.img`
  width: 48px;
  height: 48px;
  border-radius: ${settingsTheme.borderRadius.md};
  object-fit: cover;
  margin-bottom: ${settingsTheme.spacing.lg};
`;

const OrgName = styled.h3`
  font-size: ${settingsTheme.typography.fontSizes.lg};
  font-weight: ${settingsTheme.typography.fontWeights.medium};
  color: ${settingsTheme.colors.text.primary};
  margin-bottom: ${settingsTheme.spacing.sm};
`;

const OrgDescription = styled.p`
  font-size: ${settingsTheme.typography.fontSizes.sm};
  color: ${settingsTheme.colors.text.tertiary};
  margin-bottom: ${settingsTheme.spacing.lg};
`;

const OrgActions = styled.div`
  display: flex;
  gap: ${settingsTheme.spacing.sm};
  align-items: center;
  margin-top: auto;
`;

const OrgLink = styled(Link)`
  display: flex;
  align-items: center;
  font-size: ${settingsTheme.typography.fontSizes.sm};
  color: ${settingsTheme.colors.primary};
  text-decoration: none;
  gap: ${settingsTheme.spacing.xs};
  font-weight: ${settingsTheme.typography.fontWeights.medium};
  transition: ${settingsTheme.transitions.default};
  flex: 1;

  &:hover {
    color: ${settingsTheme.colors.primaryHover};
    text-decoration: underline;
  }
`;

const EditButton = styled.button`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: 1px solid ${settingsTheme.colors.border};
  background: ${settingsTheme.colors.background.main};
  border-radius: ${settingsTheme.borderRadius.sm};
  color: ${settingsTheme.colors.text.secondary};
  cursor: pointer;
  transition: ${settingsTheme.transitions.default};

  &:hover {
    background: ${settingsTheme.colors.background.light};
    border-color: ${settingsTheme.colors.primary};
    color: ${settingsTheme.colors.primary};
    transform: translateY(-1px);
  }

  &:active {
    transform: translateY(0);
  }
`;

const EmptyState = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: ${settingsTheme.spacing['2xl']} ${settingsTheme.spacing.base};
  text-align: center;
  background-color: ${settingsTheme.colors.background.light};
  border-radius: ${settingsTheme.borderRadius.lg};
  border: 1px dashed ${settingsTheme.colors.border};
`;

const EmptyStateTitle = styled.h3`
  font-size: ${settingsTheme.typography.fontSizes.lg};
  font-weight: ${settingsTheme.typography.fontWeights.medium};
  color: ${settingsTheme.colors.text.primary};
  margin-bottom: ${settingsTheme.spacing.sm};
`;

const EmptyStateDescription = styled.p`
  font-size: ${settingsTheme.typography.fontSizes.sm};
  color: ${settingsTheme.colors.text.tertiary};
  margin-bottom: ${settingsTheme.spacing.xl};
`;

const LoadingContainer = styled.div`
  width: 100%;
  min-height: 200px;
`;

const SkeletonCard = styled.div`
  ${commonStyles.card}
  border: 1px solid ${settingsTheme.colors.border};
  height: 180px;
  display: flex;
  flex-direction: column;
`;

const SkeletonIcon = styled.div`
  width: 48px;
  height: 48px;
  border-radius: ${settingsTheme.borderRadius.md};
  background-color: ${settingsTheme.colors.background.light};
  margin-bottom: ${settingsTheme.spacing.lg};
  animation: pulse 1.5s ease-in-out infinite;
`;

const SkeletonTitle = styled.div`
  height: 20px;
  width: 70%;
  background-color: ${settingsTheme.colors.background.light};
  border-radius: ${settingsTheme.borderRadius.sm};
  margin-bottom: ${settingsTheme.spacing.sm};
  animation: pulse 1.5s ease-in-out infinite;
`;

const SkeletonDescription = styled.div`
  height: 16px;
  width: 90%;
  background-color: ${settingsTheme.colors.background.light};
  border-radius: ${settingsTheme.borderRadius.sm};
  margin-bottom: ${settingsTheme.spacing.lg};
  animation: pulse 1.5s ease-in-out infinite;
`;

const SkeletonLink = styled.div`
  height: 16px;
  width: 50%;
  background-color: ${settingsTheme.colors.background.light};
  border-radius: ${settingsTheme.borderRadius.sm};
  animation: pulse 1.5s ease-in-out infinite;
`;

const GlobalStyle = styled.div`
  @keyframes pulse {
    0% {
      opacity: 0.6;
    }
    50% {
      opacity: 0.8;
    }
    100% {
      opacity: 0.6;
    }
  }
`;

const ErrorContainer = styled.div`
  padding: ${settingsTheme.spacing.base};
  background-color: ${settingsTheme.colors.error.light};
  border: 1px solid ${settingsTheme.colors.error.main};
  border-radius: ${settingsTheme.borderRadius.md};
  color: ${settingsTheme.colors.error.main};
  margin-bottom: ${settingsTheme.spacing.lg};
`;

const Modal = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
`;

const ModalContent = styled.div`
  background-color: ${settingsTheme.colors.background.main};
  padding: ${settingsTheme.spacing['2xl']};
  border-radius: ${settingsTheme.borderRadius.lg};
  width: 100%;
  max-width: 500px;
  box-shadow: ${settingsTheme.shadows.lg};

  /* Tablet */
  @media (max-width: ${settingsTheme.breakpoints.lg}) and (min-width: ${settingsTheme.breakpoints.md}) {
    max-width: 450px;
    padding: ${settingsTheme.spacing.xl};
    border-radius: ${settingsTheme.borderRadius.md};
  }

  /* Mobile */
  @media (max-width: ${settingsTheme.breakpoints.md}) {
    max-width: 95vw;
    padding: ${settingsTheme.spacing.lg};
    border-radius: ${settingsTheme.borderRadius.sm};
    margin: ${settingsTheme.spacing.sm};
  }

  /* Small mobile */
  @media (max-width: ${settingsTheme.breakpoints.sm}) {
    max-width: 100vw;
    padding: ${settingsTheme.spacing.md};
    border-radius: 0;
    margin: 0;
    height: 100vh;
    overflow-y: auto;
  }
`;

const ModalHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: ${settingsTheme.spacing.xl};
`;

const ModalTitle = styled.h3`
  font-size: ${settingsTheme.typography.fontSizes.xl};
  font-weight: ${settingsTheme.typography.fontWeights.semibold};
  color: ${settingsTheme.colors.text.primary};
`;

const CloseButton = styled.button`
  background: none;
  border: none;
  cursor: pointer;
  font-size: ${settingsTheme.typography.fontSizes['2xl']};
  color: ${settingsTheme.colors.text.tertiary};
  transition: ${settingsTheme.transitions.default};

  &:hover {
    color: ${settingsTheme.colors.text.secondary};
  }
`;

const Form = styled.form`
  display: flex;
  flex-direction: column;
  gap: ${settingsTheme.spacing.lg};
`;

const FormGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${settingsTheme.spacing.sm};
  margin-bottom: ${settingsTheme.spacing.md};
  width: 100%;
`;

const Label = styled.label`
  font-size: ${settingsTheme.typography.fontSizes.sm};
  font-weight: ${settingsTheme.typography.fontWeights.medium};
  color: ${settingsTheme.colors.text.secondary};
  margin-bottom: ${settingsTheme.spacing.xs};
`;

const Input = styled.input`
  ${commonStyles.input}
`;

const TextArea = styled.textarea`
  ${commonStyles.input}
  min-height: 100px;
  resize: vertical;
`;

const ButtonGroup = styled.div`
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  margin-top: 1rem;

  /* Mobile */
  @media (max-width: ${settingsTheme.breakpoints.md}) {
    flex-direction: column-reverse;
    gap: ${settingsTheme.spacing.sm};
  }

  /* Small mobile */
  @media (max-width: ${settingsTheme.breakpoints.sm}) {
    gap: ${settingsTheme.spacing.xs};
  }
`;

const CancelButton = styled.button`
  padding: 0.625rem 1rem;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  background-color: white;
  color: #374151;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;

  &:hover {
    background-color: #f9fafb;
  }
`;

const SubmitButton = styled.button`
  ${commonStyles.button.primary}
`;

// Image upload components
const ImageUploadContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: ${settingsTheme.spacing.md};
  margin-bottom: ${settingsTheme.spacing.lg};
`;

const OrganizationImagePreview = styled.div`
  position: relative;
  width: 80px;
  height: 80px;
  border-radius: ${settingsTheme.borderRadius.md};
  background-color: ${settingsTheme.colors.border};
  background-size: cover;
  background-position: center;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  flex-shrink: 0;
  border: 3px solid ${settingsTheme.colors.background.main};
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
  }
`;

const OrganizationImage = styled.img`
  width: 100%;
  height: 100%;
  object-fit: cover;
`;

const ImagePlaceholder = styled.div`
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 50%, #ec4899 100%);
  color: white;
  font-size: 1.5rem;
  font-weight: 700;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
`;

const UploadButton = styled.button`
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
  color: white;
  border: none;
  border-radius: ${settingsTheme.borderRadius.md};
  font-size: ${settingsTheme.typography.fontSizes.sm};
  font-weight: ${settingsTheme.typography.fontWeights.semibold};
  padding: ${settingsTheme.spacing.sm} ${settingsTheme.spacing.md};
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 2px 8px rgba(99, 102, 241, 0.3);

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(99, 102, 241, 0.4);
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
  }
`;

const HiddenFileInput = styled.input`
  display: none;
`;

const UploadOverlay = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.9) 0%, rgba(139, 92, 246, 0.9) 100%);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border-radius: ${settingsTheme.borderRadius.md};
  color: white;
  backdrop-filter: blur(4px);
  animation: fadeIn 0.3s ease-out;

  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: scale(0.9);
    }
    to {
      opacity: 1;
      transform: scale(1);
    }
  }
`;

const UploadSpinner = styled.div`
  width: 20px;
  height: 20px;
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: ${settingsTheme.spacing.xs};

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
`;

const UploadText = styled.span`
  font-size: ${settingsTheme.typography.fontSizes.xs};
  font-weight: ${settingsTheme.typography.fontWeights.semibold};
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
`;

const HoverOverlay = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border-radius: ${settingsTheme.borderRadius.md};
  color: white;
  opacity: 0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(2px);

  ${OrganizationImagePreview}:hover & {
    opacity: 1;
  }
`;

const CameraIcon = styled.span`
  font-size: 1.5rem;
  margin-bottom: ${settingsTheme.spacing.xs};
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
`;

const HoverText = styled.span`
  font-size: ${settingsTheme.typography.fontSizes.xs};
  font-weight: ${settingsTheme.typography.fontWeights.medium};
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
`;

// Crop Modal Components
const CropModalOverlay = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1001;
  backdrop-filter: blur(4px);
`;

const CropModalContainer = styled.div`
  background: ${settingsTheme.colors.background.main};
  border-radius: ${settingsTheme.borderRadius.lg};
  padding: ${settingsTheme.spacing.xl};
  width: 90vw;
  max-width: 500px;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  border: 1px solid ${settingsTheme.colors.border};

  @media (max-width: ${settingsTheme.breakpoints.md}) {
    width: 95vw;
    max-width: none;
    max-height: 90vh;
    padding: ${settingsTheme.spacing.md};
    border-radius: ${settingsTheme.borderRadius.sm};
    margin: ${settingsTheme.spacing.sm};
  }

  @media (max-width: ${settingsTheme.breakpoints.sm}) {
    width: 100vw;
    height: 100vh;
    max-height: none;
    padding: ${settingsTheme.spacing.sm};
    border-radius: 0;
    margin: 0;
  }
`;

const CropModalHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: ${settingsTheme.spacing.lg};
  padding-bottom: ${settingsTheme.spacing.md};
  border-bottom: 1px solid ${settingsTheme.colors.border};
`;

const CropModalTitle = styled.h2`
  font-size: ${settingsTheme.typography.fontSizes.xl};
  font-weight: ${settingsTheme.typography.fontWeights.semibold};
  color: ${settingsTheme.colors.text.primary};
  margin: 0;
`;

const CropModalCloseButton = styled.button`
  background: none;
  border: none;
  font-size: 1.5rem;
  color: ${settingsTheme.colors.text.secondary};
  cursor: pointer;
  padding: ${settingsTheme.spacing.xs};
  border-radius: ${settingsTheme.borderRadius.sm};
  transition: all 0.2s ease;

  &:hover {
    background-color: ${settingsTheme.colors.background.light};
    color: ${settingsTheme.colors.text.primary};
  }
`;

const CropContainer = styled.div`
  position: relative;
  width: 100%;
  height: 300px;
  background: ${settingsTheme.colors.background.lighter};
  border-radius: ${settingsTheme.borderRadius.md};
  overflow: hidden;
  margin-bottom: ${settingsTheme.spacing.lg};

  @media (max-width: ${settingsTheme.breakpoints.md}) {
    height: 250px;
    border-radius: ${settingsTheme.borderRadius.sm};
    margin-bottom: ${settingsTheme.spacing.sm};
  }

  @media (max-width: ${settingsTheme.breakpoints.sm}) {
    height: 200px;
    margin-bottom: ${settingsTheme.spacing.xs};
  }
`;

const CropControls = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${settingsTheme.spacing.md};
  margin-bottom: ${settingsTheme.spacing.lg};
`;

const ZoomControl = styled.div`
  display: flex;
  align-items: center;
  gap: ${settingsTheme.spacing.md};
`;

const ZoomLabel = styled.label`
  font-size: ${settingsTheme.typography.fontSizes.sm};
  font-weight: ${settingsTheme.typography.fontWeights.medium};
  color: ${settingsTheme.colors.text.primary};
  min-width: 60px;
`;

const ZoomSlider = styled.input`
  flex: 1;
  height: 6px;
  border-radius: 3px;
  background: ${settingsTheme.colors.background.light};
  outline: none;
  -webkit-appearance: none;

  &::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    cursor: pointer;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  }

  &::-moz-range-thumb {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    cursor: pointer;
    border: none;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  }
`;

const CropModalActions = styled.div`
  display: flex;
  gap: ${settingsTheme.spacing.md};
  justify-content: flex-end;

  @media (max-width: ${settingsTheme.breakpoints.md}) {
    flex-direction: column-reverse;
    gap: ${settingsTheme.spacing.sm};
  }
`;

const CropButton = styled.button<{ variant?: 'primary' | 'secondary' }>`
  padding: ${settingsTheme.spacing.sm} ${settingsTheme.spacing.lg};
  border-radius: ${settingsTheme.borderRadius.md};
  font-size: ${settingsTheme.typography.fontSizes.sm};
  font-weight: ${settingsTheme.typography.fontWeights.medium};
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid;
  min-width: 100px;

  ${props => props.variant === 'primary' ? `
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-color: transparent;

    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
    }
  ` : `
    background: ${settingsTheme.colors.background.light};
    color: ${settingsTheme.colors.text.primary};
    border-color: ${settingsTheme.colors.border};

    &:hover {
      background: ${settingsTheme.colors.background.main};
      border-color: ${settingsTheme.colors.text.secondary};
    }
  `}

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
  }

  @media (max-width: ${settingsTheme.breakpoints.md}) {
    width: 100%;
    min-width: auto;
    min-height: 44px;
    padding: ${settingsTheme.spacing.md} ${settingsTheme.spacing.lg};
    font-size: ${settingsTheme.typography.fontSizes.base};
    border-radius: ${settingsTheme.borderRadius.sm};
  }
`;

// Type definitions for organization data
interface Department {
  id: number;
  name: string;
  description: string | null;
}

interface Organization {
  id: number;
  name: string;
  description: string | null;
  imageUrl: string | null;
  departments: Department[];
}

export default function OrganizationsPage() {
  const router = useRouter();
  const [organizations, setOrganizations] = useState<Organization[]>([]);
  const [filteredOrganizations, setFilteredOrganizations] = useState<Organization[]>([]);

  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showModal, setShowModal] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [userRole, setUserRole] = useState<number | null>(null);

  // Edit mode state
  const [isEditMode, setIsEditMode] = useState(false);
  const [editingOrganization, setEditingOrganization] = useState<Organization | null>(null);

  // Form state for creating/editing organization
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    imageUrl: '',
  });

  // Image upload state
  const [uploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Image cropping state
  const [showCropModal, setShowCropModal] = useState(false);
  const [imageToCrop, setImageToCrop] = useState<string | null>(null);
  const [crop, setCrop] = useState<Point>({ x: 0, y: 0 });
  const [zoom, setZoom] = useState(1);
  const [croppedAreaPixels, setCroppedAreaPixels] = useState<Area | null>(null);
  const [originalFile, setOriginalFile] = useState<File | null>(null);

  // Fetch user role and organizations on component mount
  useEffect(() => {
    fetchUserRole();
    fetchOrganizations();
  }, []);

  // Set filtered organizations to all organizations
  useEffect(() => {
    setFilteredOrganizations(organizations);
  }, [organizations]);

  // Fetch user role from API
  const fetchUserRole = async () => {
    try {
      const token = getCookie('access_token');
      if (!token) return;

      const response = await fetch('/api/v1/me', {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setUserRole(data.user?.role?.id || null);
      }
    } catch (error) {
      console.error('Error fetching user role:', error);
    }
  };

  // Fetch organizations from API
  const fetchOrganizations = async () => {
    setLoading(true);
    setError(null);

    try {
      // Get token from localStorage (assuming authentication is set up)
      const token = getCookie('access_token');

      if (!token) {
        setError('Authentication required. Please log in.');
        setLoading(false);
        return;
      }

      const response = await fetch('/api/v1/organization', {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch organizations: ${response.statusText}`);
      }

      const data = await response.json();
      setOrganizations(data.organizations);
      setFilteredOrganizations(data.organizations);
    } catch (err) {
      console.error('Error fetching organizations:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch organizations');
    } finally {
      setLoading(false);
    }
  };

  // Handle form input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }));
  };

  // Handle image upload
  const handleImageUpload = async (file: File) => {
    try {
      setUploading(true);
      setError(null);
      setUploadProgress(0);

      // Upload to S3
      const uploadResult = await s3Service.uploadFile(file, 'avatars');

      if (uploadResult.success && uploadResult.data) {
        const newImageUrl = uploadResult.data.url;

        // Update the form data with the new image URL
        setFormData(prev => ({
          ...prev,
          imageUrl: newImageUrl,
        }));
      } else {
        throw new Error(uploadResult.error || 'Failed to upload image');
      }
    } catch (err: any) {
      console.error('Error uploading image:', err);
      setError(err.message || 'Failed to upload image. Please try again.');
    } finally {
      setUploading(false);
      setUploadProgress(0);
    }
  };

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      handleFileForCropping(file);
    }
  };

  const handleFileForCropping = async (file: File) => {
    try {
      // Validate file type
      const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
      if (!allowedTypes.includes(file.type)) {
        throw new Error('Please select a valid image file (JPEG, PNG, GIF, or WebP)');
      }

      // Validate file size (5MB for avatars)
      const maxSize = 5 * 1024 * 1024; // 5MB
      if (file.size > maxSize) {
        throw new Error('Image size must be less than 5MB');
      }

      // Create image URL for cropping
      const imageUrl = URL.createObjectURL(file);
      setImageToCrop(imageUrl);
      setOriginalFile(file);
      setShowCropModal(true);
      setError(null);
    } catch (err: any) {
      console.error('Error preparing image for cropping:', err);
      setError(err.message || 'Failed to prepare image for cropping.');
    }
  };

  const onCropComplete = useCallback((_croppedArea: Area, croppedAreaPixels: Area) => {
    setCroppedAreaPixels(croppedAreaPixels);
  }, []);

  const createCroppedImage = async (): Promise<File | null> => {
    if (!imageToCrop || !croppedAreaPixels || !originalFile) return null;

    return new Promise((resolve) => {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      const image = new Image();

      image.onload = () => {
        const { width, height, x, y } = croppedAreaPixels;

        canvas.width = width;
        canvas.height = height;

        ctx?.drawImage(
          image,
          x,
          y,
          width,
          height,
          0,
          0,
          width,
          height
        );

        canvas.toBlob((blob) => {
          if (blob) {
            const croppedFile = new File([blob], originalFile.name, {
              type: originalFile.type,
              lastModified: Date.now(),
            });
            resolve(croppedFile);
          } else {
            resolve(null);
          }
        }, originalFile.type, 0.9);
      };

      image.src = imageToCrop;
    });
  };

  const handleCropConfirm = async () => {
    try {
      const croppedFile = await createCroppedImage();
      if (croppedFile) {
        setShowCropModal(false);
        setImageToCrop(null);
        URL.revokeObjectURL(imageToCrop!);
        await handleImageUpload(croppedFile);
      }
    } catch (err: any) {
      console.error('Error cropping image:', err);
      setError('Failed to crop image. Please try again.');
    }
  };

  const handleCropCancel = () => {
    setShowCropModal(false);
    setImageToCrop(null);
    setCrop({ x: 0, y: 0 });
    setZoom(1);
    setCroppedAreaPixels(null);
    setOriginalFile(null);
    if (imageToCrop) {
      URL.revokeObjectURL(imageToCrop);
    }
    // Reset file input
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const triggerFileSelect = () => {
    fileInputRef.current?.click();
  };

  const resetModal = () => {
    setFormData({ name: '', description: '', imageUrl: '' });
    setShowModal(false);
    setError(null);
    // Reset edit mode states
    setIsEditMode(false);
    setEditingOrganization(null);
    // Reset image upload states
    setUploading(false);
    setUploadProgress(0);
    // Reset crop states
    setShowCropModal(false);
    setImageToCrop(null);
    setCrop({ x: 0, y: 0 });
    setZoom(1);
    setCroppedAreaPixels(null);
    setOriginalFile(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const handleEditOrganization = (organization: Organization) => {
    setIsEditMode(true);
    setEditingOrganization(organization);
    setFormData({
      name: organization.name,
      description: organization.description || '',
      imageUrl: organization.imageUrl || '',
    });
    setShowModal(true);
    setError(null);
  };

  const handleCreateOrganization = () => {
    setIsEditMode(false);
    setEditingOrganization(null);
    setFormData({ name: '', description: '', imageUrl: '' });
    setShowModal(true);
    setError(null);
  };

  // Handle form submission to create or edit organization
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setSubmitting(true);

    try {
      const token = getCookie('access_token');

      if (!token) {
        setError('Authentication required. Please log in.');
        setSubmitting(false);
        return;
      }

      const url = '/api/v1/organization';
      const method = isEditMode ? 'PATCH' : 'POST';

      // Prepare request body
      const requestBody = isEditMode
        ? { ...formData, id: editingOrganization?.id }
        : formData;

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify(requestBody),
      });

      if (!response.ok) {
        const errorData = await response.json();
        const action = isEditMode ? 'update' : 'create';
        throw new Error(errorData.error || `Failed to ${action} organization`);
      }

      // Reset form and close modal
      resetModal();

      // Refresh organizations list
      fetchOrganizations();
    } catch (err) {
      console.error(`Error ${isEditMode ? 'updating' : 'creating'} organization:`, err);
      const action = isEditMode ? 'update' : 'create';
      setError(err instanceof Error ? err.message : `Failed to ${action} organization`);
    } finally {
      setSubmitting(false);
    }
  };

  

  return (
    <SettingsLayout>
      <OrganizationsContainer>
        <Header>
          <Title>องค์กร</Title>
          {userRole === 1 && (
            <AddButton onClick={handleCreateOrganization}>
              <Plus size={16} />
              เพิ่มองค์กร
            </AddButton>
          )}
        </Header>

        {error && <ErrorContainer>{error}</ErrorContainer>}

        <GlobalStyle />
        {loading ? (
          <LoadingContainer>
            <OrganizationGrid>
              {[...Array(4)].map((_, index) => (
                <SkeletonCard key={index}>
                  <SkeletonIcon />
                  <SkeletonTitle />
                  <SkeletonDescription />
                  <SkeletonLink />
                </SkeletonCard>
              ))}
            </OrganizationGrid>
          </LoadingContainer>
        ) : filteredOrganizations.length > 0 ? (
          <OrganizationGrid>
            {filteredOrganizations.map(org => (
              <OrganizationCard key={org.id}>
                {org.imageUrl ? (
                  <OrgImage src={org.imageUrl} alt={`${org.name} logo`} />
                ) : (
                  <OrgIcon>
                    <Building2 size={24} />
                  </OrgIcon>
                )}
                <OrgName>{org.name}</OrgName>
                <OrgDescription>{org.description || 'ไม่มีรายละเอียด'}</OrgDescription>
                <OrgActions>
                  <OrgLink href={`/settings/organizations/${org.id}/department`}>
                    จัดการ
                    <ArrowRight size={14} />
                  </OrgLink>
                  {userRole === 1 && (
                    <EditButton
                      onClick={() => handleEditOrganization(org)}
                      title="แก้ไข"
                    >
                      <Edit2 size={16} />
                    </EditButton>
                  )}
                </OrgActions>
              </OrganizationCard>
            ))}
          </OrganizationGrid>
        ) : (
          <EmptyState>
            <Building2 size={48} color="#9ca3af" />
            <EmptyStateTitle>ไม่พบหน่วยงาน</EmptyStateTitle>
            <EmptyStateDescription>
              {userRole === 1
                ? "คุณยังไม่ได้สร้างองค์กร"
                : "คุณไม่มีสิทธิ์เข้าถึงองค์กร"
              }
            </EmptyStateDescription>
            {userRole === 1 && (
              <AddButton onClick={handleCreateOrganization}>
                <Plus size={16} />
                เพิ่มองค์กร
              </AddButton>
            )}
          </EmptyState>
        )}
      </OrganizationsContainer>

      {/* Add Organization Modal */}
      {showModal && (
        <Modal>
          <ModalContent>
            <ModalHeader>
              <ModalTitle>
                {isEditMode ? 'แก้ไของค์กร' : 'สร้างองค์กรใหม่'}
              </ModalTitle>
              <CloseButton onClick={resetModal}>&times;</CloseButton>
            </ModalHeader>
            <Form onSubmit={handleSubmit}>
              <FormGroup>
                <Label htmlFor="name">ชื่อองค์กร *</Label>
                <Input
                  id="name"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  required
                />
              </FormGroup>
              <FormGroup>
                <Label htmlFor="description">รายละเอียด</Label>
                <TextArea
                  id="description"
                  name="description"
                  value={formData.description}
                  onChange={handleInputChange}
                />
              </FormGroup>
              <FormGroup>
                <Label>โลโก้องค์กร</Label>
                <ImageUploadContainer>
                  <OrganizationImagePreview onClick={triggerFileSelect}>
                    {formData.imageUrl ? (
                      <OrganizationImage src={formData.imageUrl} alt="Organization logo" />
                    ) : (
                      <ImagePlaceholder>
                        {formData.name ? formData.name.charAt(0).toUpperCase() : 'O'}
                      </ImagePlaceholder>
                    )}
                    {uploading && (
                      <UploadOverlay>
                        <UploadSpinner />
                        <UploadText>กำลังอัปโหลด...</UploadText>
                      </UploadOverlay>
                    )}
                    {!uploading && (
                      <HoverOverlay>
                        <CameraIcon>📷</CameraIcon>
                        <HoverText>อัปโหลด</HoverText>
                      </HoverOverlay>
                    )}
                  </OrganizationImagePreview>
                  <UploadButton type="button" onClick={triggerFileSelect} disabled={uploading}>
                    {uploading ? 'กำลังอัปโหลด...' : '📁 เลือกไฟล์'}
                  </UploadButton>
                  <HiddenFileInput
                    ref={fileInputRef}
                    type="file"
                    accept="image/jpeg,image/png,image/gif,image/webp"
                    onChange={handleFileSelect}
                  />
                </ImageUploadContainer>
                <small style={{ color: '#6b7280', fontSize: '0.75rem' }}>
                  JPG, PNG, GIF or WebP • Max 5MB
                </small>
              </FormGroup>
              <ButtonGroup>
                <CancelButton type="button" onClick={resetModal}>
                  ยกเลิก
                </CancelButton>
                <SubmitButton type="submit" disabled={submitting || !formData.name.trim()}>
                  {submitting ? (
                    <>
                      <Loader2 size={16} className="animate-spin" />
                      {isEditMode ? 'กำลังอัปเดต...' : 'กำลังสร้าง...'}
                    </>
                  ) : (
                    isEditMode ? 'อัปเดตองค์กร' : 'สร้างองค์กร'
                  )}
                </SubmitButton>
              </ButtonGroup>
            </Form>
          </ModalContent>
        </Modal>
      )}

      {/* Crop Modal */}
      {showCropModal && imageToCrop && (
        <CropModalOverlay>
          <CropModalContainer>
            <CropModalHeader>
              <CropModalTitle>ตัดรูป</CropModalTitle>
              <CropModalCloseButton onClick={handleCropCancel}>
                ×
              </CropModalCloseButton>
            </CropModalHeader>

            <CropContainer>
              <Cropper
                image={imageToCrop || ""}
                crop={crop}
                zoom={zoom}
                aspect={1}
                onCropChange={setCrop}
                onZoomChange={setZoom}
                onCropComplete={onCropComplete}
                cropShape="round"
                showGrid={false}
              />
            </CropContainer>

            <CropControls>
              <ZoomControl>
                <ZoomLabel>Zoom:</ZoomLabel>
                <ZoomSlider
                  type="range"
                  min={1}
                  max={3}
                  step={0.1}
                  value={zoom}
                  onChange={(e) => setZoom(Number(e.target.value))}
                />
              </ZoomControl>
            </CropControls>

            <CropModalActions>
              <CropButton variant="secondary" onClick={handleCropCancel}>
                ยกเลิก
              </CropButton>
              <CropButton variant="primary" onClick={handleCropConfirm}>
                ตัดรูป
              </CropButton>
            </CropModalActions>
          </CropModalContainer>
        </CropModalOverlay>
      )}
    </SettingsLayout>
  );
}

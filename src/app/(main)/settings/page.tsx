'use client';

import React, { useState } from 'react';
import SettingsLayout from './components/SettingsLayout';
import ProfileSettings from './components/ProfileSettings';
import ChangePassword from './components/ChangePassword';
import PointHistory from './components/PointHistory';
import TabNavigation, { Tab } from './components/TabNavigation';

export default function SettingsPage() {
  const tabs: Tab[] = [
    { id: 'profile', label: 'ข้อมูลส่วนตัว' },
    { id: 'change-password', label: 'เปลี่ยนรหัสผ่าน' },
    { id: 'point-history', label: 'ประวัติคะแนน' },
  ];

  const [activeTab, setActiveTab] = useState('profile');

  const renderTabContent = () => {
    switch (activeTab) {
      case 'profile':
        return <ProfileSettings />;
      case 'change-password':
        return <ChangePassword />;
      case 'point-history':
        return <PointHistory />;
      default:
        return <ProfileSettings />;
    }
  };

  return (
    <SettingsLayout>
      <TabNavigation tabs={tabs} activeTab={activeTab} onTabChange={setActiveTab} />
      {renderTabContent()}
    </SettingsLayout>
  );
}

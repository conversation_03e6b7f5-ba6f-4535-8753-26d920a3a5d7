'use client';

import React, { useState, useEffect, useRef } from 'react';
import styled from 'styled-components';
import { DndProvider, useDrag, useDrop } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import { TouchBackend } from 'react-dnd-touch-backend';
import {
  Calendar,
  Clock,
  User,
  Award,
  Plus,
  Building,
  Briefcase,
  ExternalLink,
  Star,
  AlertTriangle,
  MoreVertical,
  MessageSquare,
  Paperclip,
  CheckSquare,
  Timer,
  Info,
  CheckCircle,
  Check,
  Edit,
} from 'lucide-react';
import { useAuth } from '@/hooks/useAuth';
import { useRouter } from 'next/navigation';
import useUserStore from '@/store/userStore';
import { USER_ROLES } from '@/constants/roles';
import TaskChatButton from '@/components/TaskChatButton';
import { useSocketEmit } from '@/hooks/useSocket';

import { appTheme as settingsTheme } from '@/app/theme';
// Types based on the Prisma schema
interface TaskStatusTransition {
  id: number;
  fromStatusId: number;
  toStatusId: number;
  allowOwner: boolean;
  allowAdmin: boolean;
  allowMember: boolean;
  description?: string;
  fromStatus?: TaskStatus;
  toStatus?: TaskStatus;
}

interface TaskStatus {
  id: number;
  name: string;
  displayName: string;
  color: string;
  description?: string;
  toStatusTransitions?: TaskStatusTransition[];
}

interface User {
  id: number;
  name: string;
  email: string;
  imageUrl?: string;
}

interface Organization {
  id: number;
  name: string;
}

interface Department {
  id: number;
  name: string;
  organization?: Organization;
}

interface ApiMember {
  user: {
    id: number;
    firstName: string;
    lastName: string;
    email: string;
    imageUrl?: string;
  };
  departmentId: number;
}

interface TaskAssignment {
  id: number;
  taskId: number;
  userId: number;
  assignedAt: Date;
  assignedBy?: number;
  isLeader: boolean;
  isActive: boolean;
  user: {
    id: number;
    firstName: string;
    lastName: string;
    email: string;
    imageUrl?: string;
  };
}

interface Task {
  id: number;
  taskTitle: string;
  taskDescription?: string;
  createdByUserId: number;
  organizationId?: number;
  departmentId?: number;
  organization?: Organization;
  department?: Department;
  taskAssignments: TaskAssignment[];
  statusId: number;
  points?: number;
  isClaimPoint?: boolean;
  dueDate?: Date;
  createdAt: Date;
  updatedAt: Date;
  createdByUser: User;
  status: TaskStatus;
}

// Styled components
const KanbanContainer = styled.div`
  padding: 1.5rem;
  min-height: calc(100vh - 80px);
  // overflow: hidden;
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.075), rgba(0, 0, 0, 0));
  // border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: ${settingsTheme.borderRadius.lg} 0 0 0;

  /* Tablet */
  @media (max-width: 1023px) {
    padding: 1.25rem;
  }

  /* Mobile */
  @media (max-width: 767px) {
    padding: 1rem;
    min-height: calc(100vh - 80px);
  }

  /* Small mobile */
  @media (max-width: 480px) {
    padding: 0.75rem;
  }
`;

const KanbanHeader = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: ${settingsTheme.spacing.lg};
  border-radius: ${settingsTheme.borderRadius.lg};
  background-color: #ffffff;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  margin-bottom: 1rem;
  z-index: 1;

  /* Tablet */
  @media (max-width: 1023px) {
    padding: ${settingsTheme.spacing.md};
    margin-bottom: 0.75rem;
  }

  /* Mobile */
  @media (max-width: 767px) {
    flex-direction: column;
    gap: 0.75rem;
    padding: ${settingsTheme.spacing.md};
    align-items: stretch;
  }

  /* Small mobile */
  @media (max-width: 480px) {
    padding: ${settingsTheme.spacing.sm};
    gap: 0.5rem;
  }
`;

const TaskSummary = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 1rem;
  padding: 0.75rem;
  background-color: #ffffff;
  border-radius: 8px;
  font-size: 0.875rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);

  /* Tablet */
  @media (max-width: 1023px) {
    padding: 0.625rem;
    gap: 0.375rem;
    margin-bottom: 0.75rem;
  }

  /* Mobile */
  @media (max-width: 767px) {
    padding: 0.5rem;
    gap: 0.25rem;
    font-size: 0.8rem;
    margin-bottom: 0.5rem;
  }

  /* Small mobile */
  @media (max-width: 480px) {
    padding: 0.5rem;
    gap: 0.25rem;
    font-size: 0.75rem;
  }
`;

const SummaryBadge = styled.div<{ $bgColor?: string }>`
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.75rem;
  border-radius: 4px;
  background: ${props => props.$bgColor || '#e2e8f0'};
  font-size: 0.7rem;
  font-weight: 400;
  color: #6b7280;

  .count {
    font-weight: 600;
    margin-right: 0.35rem;
  }

  /* Tablet */
  @media (max-width: 1023px) {
    padding: 0.2rem 0.6rem;
    font-size: 0.65rem;

    .count {
      margin-right: 0.25rem;
    }
  }

  /* Mobile */
  @media (max-width: 767px) {
    padding: 0.15rem 0.5rem;
    font-size: 0.6rem;

    .count {
      margin-right: 0.2rem;
    }
  }

  /* Small mobile */
  @media (max-width: 480px) {
    padding: 0.125rem 0.4rem;
    font-size: 0.55rem;

    .count {
      margin-right: 0.15rem;
    }
  }
`;

const KanbanTitle = styled.h1`
  font-size: ${settingsTheme.typography.fontSizes['2xl']};
  font-weight: ${settingsTheme.typography.fontWeights.bold};
  color: ${settingsTheme.colors.text.primary};
  margin: 0;
  display: flex;
  align-items: center;
  gap: ${settingsTheme.spacing.md};
  @media (max-width: ${settingsTheme.breakpoints.md}) {
    font-size: ${settingsTheme.typography.fontSizes['xl']};
  }
`;

const KanbanBoard = styled.div`
  display: flex;
  gap: 1rem;
  height: calc(100% - 60px);
  overflow-x: auto;
  padding-bottom: 1rem;

  /* Minimal scrollbar styling */
  &::-webkit-scrollbar {
    height: 6px;
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 6px;
  }

  /* Hide scrollbar when not hovering/scrolling */
  &::-webkit-scrollbar-thumb {
    visibility: hidden;
  }

  &:hover::-webkit-scrollbar-thumb,
  &:active::-webkit-scrollbar-thumb,
  &:focus::-webkit-scrollbar-thumb {
    visibility: visible;
  }

  /* Equal width columns */
  & > * {
    flex: 1;
    min-width: 300px;
  }

  /* Tablet */
  @media (max-width: 1023px) {
    gap: 0.75rem;
    padding-bottom: 0.75rem;

    & > * {
      min-width: 280px;
    }
  }

  /* Mobile */
  @media (max-width: 767px) {
    gap: 0.5rem;
    padding-bottom: 0.5rem;

    & > * {
      min-width: 260px;
      flex: 0 0 260px; /* Fixed width for better mobile scrolling */
    }
  }

  /* Small mobile */
  @media (max-width: 480px) {
    gap: 0.375rem;

    & > * {
      min-width: 240px;
      flex: 0 0 240px;
    }
  }

  /* Very small mobile */
  @media (max-width: 360px) {
    gap: 0.25rem;

    & > * {
      min-width: 220px;
      flex: 0 0 220px;
    }
  }
`;

const Lane = styled.div`
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0.2));
  border-radius: 8px;
  min-height: 100%;
  display: flex;
  flex-direction: column;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: box-shadow 0.2s ease;

  &:hover {
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  }

  /* Tablet */
  @media (max-width: 1023px) {
    border-radius: 6px;
  }

  /* Mobile */
  @media (max-width: 767px) {
    min-width: 260px;
    border-radius: 6px;
  }

  /* Small mobile */
  @media (max-width: 480px) {
    min-width: 240px;
    border-radius: 4px;
  }

  /* Very small mobile */
  @media (max-width: 360px) {
    min-width: 220px;
  }
`;

const LaneHeader = styled.div<{ color: string }>`
  padding: 1rem;
  border-bottom: 1px solid #e0e0e0;
  background-color: #ffffff;
  border-radius: 8px 8px 0 0;
  display: flex;
  align-items: center;
  justify-content: space-between;

  /* Tablet */
  @media (max-width: 1023px) {
    padding: 0.75rem;
    border-radius: 6px 6px 0 0;
  }

  /* Mobile */
  @media (max-width: 767px) {
    padding: 0.625rem;
    border-radius: 6px 6px 0 0;
  }

  /* Small mobile */
  @media (max-width: 480px) {
    padding: 0.5rem;
    border-radius: 4px 4px 0 0;
  }
`;

const LaneTitle = styled.h2<{ $textColor?: string }>`
  font-size: 0.75rem;
  font-weight: 700;
  color: #1f2937;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;

  /* Tablet */
  @media (max-width: 1023px) {
    font-size: 0.7rem;
    gap: 0.375rem;
  }

  /* Mobile */
  @media (max-width: 767px) {
    font-size: 0.65rem;
    gap: 0.25rem;
  }

  /* Small mobile */
  @media (max-width: 480px) {
    font-size: 0.6rem;
    gap: 0.25rem;
  }
`;

const LaneCount = styled.span<{ $bgColor?: string }>`
  background: ${props => props.$bgColor || '#e2e8f0'};
  border-radius: 12px;
  padding: 2px 8px;
  font-size: 0.75rem;
  margin-left: 8px;
  color: #ffffff;
  font-weight: 600;

  /* Tablet */
  @media (max-width: 1023px) {
    padding: 1px 6px;
    font-size: 0.7rem;
    margin-left: 6px;
  }

  /* Mobile */
  @media (max-width: 767px) {
    padding: 1px 5px;
    font-size: 0.65rem;
    margin-left: 4px;
    border-radius: 8px;
  }

  /* Small mobile */
  @media (max-width: 480px) {
    padding: 1px 4px;
    font-size: 0.6rem;
    margin-left: 3px;
  }
`;

const LaneBody = styled.div`
  padding: 0.75rem;
  flex: 1;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;

  /* Minimal scrollbar styling */
  &::-webkit-scrollbar {
    width: 6px;
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 6px;
  }

  /* Hide scrollbar when not hovering/scrolling */
  &::-webkit-scrollbar-thumb {
    visibility: hidden;
  }

  &:hover::-webkit-scrollbar-thumb,
  &:active::-webkit-scrollbar-thumb,
  &:focus::-webkit-scrollbar-thumb {
    visibility: visible;
  }

  /* Tablet */
  @media (max-width: 1023px) {
    padding: 0.625rem;
    gap: 0.375rem;
  }

  /* Mobile */
  @media (max-width: 767px) {
    padding: 0.5rem;
    gap: 0.375rem;
  }

  /* Small mobile */
  @media (max-width: 480px) {
    padding: 0.375rem;
    gap: 0.25rem;
  }
`;

const EmptyLaneMessage = styled.div`
  color: #6b7280;
  text-align: center;
  padding: 1rem;
  font-size: 0.875rem;
  width: 100%;

  /* Tablet */
  @media (max-width: 1023px) {
    padding: 0.75rem;
    font-size: 0.8rem;
  }

  /* Mobile */
  @media (max-width: 767px) {
    padding: 0.5rem;
    font-size: 0.75rem;
  }

  /* Small mobile */
  @media (max-width: 480px) {
    padding: 0.5rem;
    font-size: 0.7rem;
  }
`;

// Using transient props with $ prefix to prevent them from being passed to the DOM element
const Card = styled.div<{ $backgroundColor?: string }>`
  background-color: ${settingsTheme.colors.background.main};
  border-radius: ${settingsTheme.borderRadius.lg};
  padding: ${settingsTheme.spacing.md};
  box-shadow: ${settingsTheme.shadows.card};
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid ${settingsTheme.colors.background.lighter};
  position: relative;
  overflow: hidden;

  /* Enhanced hover effect */
  &:hover {
    box-shadow: ${settingsTheme.shadows.cardHover};
    transform: translateY(-2px);
    border-color: ${settingsTheme.colors.primary}20;
  }

  /* Subtle gradient overlay for depth */
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, ${settingsTheme.colors.primary}20, transparent);
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  &:hover::before {
    opacity: 1;
  }

  /* Improved spacing for child elements */
  > * + * {
    margin-top: ${settingsTheme.spacing.sm};
  }

  /* Tablet */
  @media (max-width: 1023px) {
    padding: ${settingsTheme.spacing.sm};
    border-radius: ${settingsTheme.borderRadius.md};

    &:hover {
      transform: translateY(-1px);
    }
  }

  /* Mobile */
  @media (max-width: 767px) {
    padding: ${settingsTheme.spacing.sm};
    border-radius: ${settingsTheme.borderRadius.md};

    /* Reduce hover effects on mobile for better touch experience */
    &:hover {
      transform: none;
      box-shadow: ${settingsTheme.shadows.card};
    }

    /* Improve touch targets */
    min-height: 44px;
  }

  /* Small mobile */
  @media (max-width: 480px) {
    padding: ${settingsTheme.spacing.xs};
    border-radius: ${settingsTheme.borderRadius.sm};

    > * + * {
      margin-top: ${settingsTheme.spacing.xs};
    }
  }
`;

const CardTitle = styled.h3<{ $textColor?: string }>`
  font-size: ${settingsTheme.typography.fontSizes.base};
  font-weight: ${settingsTheme.typography.fontWeights.semibold};
  color: ${settingsTheme.colors.text.primary};
  line-height: 1.4;
  margin: 0 0 ${settingsTheme.spacing.sm} 0;
  word-break: break-word;
  hyphens: auto;

  /* Allow title to wrap to 2 lines max */
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  max-height: 2.8rem; /* Approximately 2 lines */

  @media (max-width: ${settingsTheme.breakpoints.md}) {
    font-size: ${settingsTheme.typography.fontSizes.sm};
    max-height: 2.4rem;
  }
`;

const CardDescription = styled.div<{ $textColor?: string; $isExpanded?: boolean }>`
  font-size: ${settingsTheme.typography.fontSizes.sm};
  color: ${settingsTheme.colors.text.secondary};
  line-height: 1.5;
  margin: ${settingsTheme.spacing.sm} 0 ${settingsTheme.spacing.md} 0;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: ${props => (props.$isExpanded ? 'unset' : '3')};
  -webkit-box-orient: vertical;
  max-height: ${props => (props.$isExpanded ? 'none' : '4.5rem')}; /* Approximately 3 lines */
  transition:
    max-height 0.3s ease,
    opacity 0.2s ease;
  position: relative;

  /* Enhanced typography for better readability */
  font-weight: ${settingsTheme.typography.fontWeights.normal};
  letter-spacing: 0.01em;
  word-break: break-word;
  hyphens: auto;

  /* Improved gradient fade effect for truncated text */
  &:not([data-expanded='true'])::after {
    content: '';
    position: absolute;
    bottom: 0;
    right: 0;
    width: 30%;
    height: 1.5rem;
    background: linear-gradient(to right, transparent, ${settingsTheme.colors.background.main});
    pointer-events: none;
  }

  /* Enhanced HTML content styling from rich text editor */
  p {
    margin: 0 0 ${settingsTheme.spacing.xs} 0;
    line-height: 1.5;

    &:last-child {
      margin-bottom: 0;
    }

    &:first-child {
      margin-top: 0;
    }
  }

  strong,
  b {
    font-weight: ${settingsTheme.typography.fontWeights.semibold};
    color: ${settingsTheme.colors.text.primary};
  }

  em,
  i {
    font-style: italic;
    color: ${settingsTheme.colors.text.primary};
  }

  /* Underline text styling */
  u {
    text-decoration: underline;
  }

  /* Strikethrough text styling */
  s, del {
    text-decoration: line-through;
  }

  /* Heading styles - consistent with TextEditor */
  h1, h2, h3, h4, h5, h6 {
    margin: 6px 0 3px 0;
    font-weight: ${settingsTheme.typography.fontWeights.semibold};
    line-height: 1.3;
    color: ${settingsTheme.colors.text.primary};
  }

  h1 { font-size: 1.3em; }
  h2 { font-size: 1.2em; }
  h3 { font-size: 1.1em; }
  h4 { font-size: 1.05em; }
  h5 { font-size: 1em; }
  h6 { font-size: 0.95em; font-weight: ${settingsTheme.typography.fontWeights.medium}; }

  /* Horizontal rule styling */
  hr {
    border: none;
    border-top: 1px solid ${settingsTheme.colors.border};
    margin: ${settingsTheme.spacing.sm} 0;
    height: 0;
  }

  ul,
  ol {
    margin: ${settingsTheme.spacing.xs} 0;
    padding-left: 1.25rem;

    &:first-child {
      margin-top: 0;
    }

    &:last-child {
      margin-bottom: 0;
    }
  }

  li {
    margin: ${settingsTheme.spacing.xs} 0;
    line-height: 1.4;

    &:first-child {
      margin-top: 0;
    }

    &:last-child {
      margin-bottom: 0;
    }
  }

  ul li {
    list-style-type: disc;
    margin-left: 0;
  }

  ol li {
    list-style-type: decimal;
    margin-left: 0;
  }

  a {
    color: ${settingsTheme.colors.primary};
    text-decoration: none;
    font-weight: ${settingsTheme.typography.fontWeights.medium};
    transition: color 0.2s ease;

    &:hover {
      color: ${settingsTheme.colors.primaryHover};
      text-decoration: underline;
    }
  }

  /* Code and preformatted text */
  code {
    background-color: ${settingsTheme.colors.background.lighter};
    color: ${settingsTheme.colors.text.primary};
    padding: 0.125rem 0.25rem;
    border-radius: ${settingsTheme.borderRadius.sm};
    font-size: 0.85em;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  }

  pre {
    background-color: ${settingsTheme.colors.background.lighter};
    padding: ${settingsTheme.spacing.sm};
    border-radius: ${settingsTheme.borderRadius.sm};
    overflow-x: auto;
    margin: ${settingsTheme.spacing.sm} 0;

    code {
      background: none;
      padding: 0;
    }
  }

  /* Blockquotes */
  blockquote {
    border-left: 3px solid ${settingsTheme.colors.primary};
    padding-left: ${settingsTheme.spacing.sm};
    margin: ${settingsTheme.spacing.sm} 0;
    font-style: italic;
    color: ${settingsTheme.colors.text.secondary};
  }

  /* Remove extra spacing from nested elements when collapsed */
  ${props =>
    !props.$isExpanded &&
    `
    br {
      display: none;
    }

    p + p {
      margin-top: 0;
    }
  `}

  /* Responsive design */
  @media (max-width: ${settingsTheme.breakpoints.md}) {
    font-size: ${settingsTheme.typography.fontSizes.xs};
    -webkit-line-clamp: ${props => (props.$isExpanded ? 'unset' : '2')};
    max-height: ${props => (props.$isExpanded ? 'none' : '3rem')};
    margin: ${settingsTheme.spacing.xs} 0 ${settingsTheme.spacing.sm} 0;
  }

  @media (max-width: ${settingsTheme.breakpoints.sm}) {
    -webkit-line-clamp: ${props => (props.$isExpanded ? 'unset' : '2')};
    max-height: ${props => (props.$isExpanded ? 'none' : '2.5rem')};
  }
`;

const CardMeta = styled.div<{ $textColor?: string }>`
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  font-size: 0.75rem;
  color: #6b7280;
`;

const CardMetaItem = styled.div<{ $textColor?: string }>`
  display: flex;
  align-items: center;
  gap: 0.25rem;
  color: #6b7280;
`;

const Badge = styled.span<{ color?: string }>`
  background: ${props => props.color || '#e0e0e0'};
  color: white;
  border-radius: 4px;
  padding: 2px 6px;
  font-size: 0.75rem;
  font-weight: 500;
  display: inline-block;
  margin-right: 0.5rem;
  margin-bottom: 0.5rem;
`;

const InfoBadge = styled.span<{ $bgColor?: string; $textColor?: string }>`
  background: ${props => props.$bgColor || settingsTheme.colors.background.lighter};
  color: ${props => props.$textColor || settingsTheme.colors.text.tertiary};
  border-radius: ${settingsTheme.borderRadius.sm};
  padding: 0.125rem 0.375rem;
  font-size: 0.625rem;
  font-weight: ${settingsTheme.typography.fontWeights.medium};
  display: flex;
  align-items: center;
  gap: 0.125rem;
  margin-right: 0.25rem;
  border: 1px solid ${settingsTheme.colors.background.lighter};
  transition: all 0.2s ease;

  &:hover {
    background: ${settingsTheme.colors.background.light};
    border-color: ${settingsTheme.colors.primary}30;
  }
`;

// Container for organization/department badges
const InfoBadgeContainer = styled.div`
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 0.25rem;
  margin: 0.375rem 0;
  padding: 0.25rem 0;
  border-top: 1px solid ${settingsTheme.colors.background.lighter}20;
  border-bottom: 1px solid ${settingsTheme.colors.background.lighter}20;

  &:empty {
    display: none;
  }
`;

// Using transient props with $ prefix to prevent them from being passed to the DOM element
const CardFooter = styled.div<{ $borderColor?: string; $textColor?: string }>`
  margin-top: ${settingsTheme.spacing.md};
  padding-top: ${settingsTheme.spacing.sm};
  border-top: 1px solid ${settingsTheme.colors.background.lighter};
  font-size: ${settingsTheme.typography.fontSizes.xs};
  color: ${settingsTheme.colors.text.tertiary};

  /* Enhanced visual separation */
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: -1px;
    left: -${settingsTheme.spacing.md};
    right: -${settingsTheme.spacing.md};
    height: 1px;
    background: linear-gradient(
      90deg,
      transparent,
      ${settingsTheme.colors.background.lighter},
      transparent
    );
  }
`;

// Popover components for task card menu
const PopoverMenu = styled.div<{ $isOpen: boolean }>`
  position: absolute;
  top: 24px;
  right: 0;
  background-color: white;
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  display: ${props => (props.$isOpen ? 'block' : 'none')};
  z-index: 99999;
  min-width: 110px;

  &::before {
    content: '';
    position: absolute;
    top: -4px;
    right: 8px;
    transform: rotate(45deg);
    width: 8px;
    height: 8px;
    background-color: white;
    border-radius: 1px;
    box-shadow: -1px -1px 3px rgba(0, 0, 0, 0.05);
  }
`;

const UsersPopoverMenu = styled.div<{ $isOpen: boolean }>`
  position: absolute;
  top: 40px;
  left: 0;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  overflow: hidden;
  display: ${props => (props.$isOpen ? 'block' : 'none')};
  z-index: 99999;
  min-width: 200px;
  max-width: 280px;

  &::before {
    content: '';
    position: absolute;
    top: -4px;
    left: 16px;
    transform: rotate(45deg);
    width: 8px;
    height: 8px;
    background-color: white;
    border-radius: 1px;
    box-shadow: -1px -1px 3px rgba(0, 0, 0, 0.05);
  }
`;

const UsersPopoverHeader = styled.div`
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
  font-size: 14px;
  font-weight: 600;
  color: #333;
`;

const UsersPopoverContent = styled.div`
  max-height: 200px;
  overflow-y: auto;
`;

const UserPopoverItem = styled.div`
  display: flex;
  align-items: center;
  padding: 8px 16px;
  gap: 12px;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: #f8f9fa;
  }
`;

const UserPopoverAvatar = styled.div<{ $imageUrl?: string }>`
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: #e5e7eb;
  background-image: ${props => (props.$imageUrl ? `url(${props.$imageUrl})` : 'none')};
  background-size: cover;
  background-position: center;
  border: 2px solid white;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #6b7280;
  font-weight: 500;
  font-size: 12px;
  flex-shrink: 0;
`;

const UserPopoverInfo = styled.div`
  flex: 1;
  min-width: 0;
`;

const UserPopoverName = styled.div`
  font-size: 14px;
  font-weight: 500;
  color: #333;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
`;

const UserPopoverEmail = styled.div`
  font-size: 12px;
  color: #666;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
`;

const PopoverItem = styled.div`
  .btn {
    display: flex;
    align-items: center;
    padding: 0.5rem 0.75rem;
    text-decoration: none;
    color: #98979c;
    width: 100%;
    text-align: left;
    background: none;
    border: none;
    cursor: pointer;
    font-size: 0.85rem;

    &:hover {
      background: linear-gradient(135deg, rgba(0, 0, 0, 0.075), rgba(0, 0, 0, 0));
    }
  }
`;

const PopoverIcon = styled.span`
  margin-right: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
`;

const DetailButton = styled.button`
  background: transparent;
  border: none;
  color: #98979c;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s ease;
  position: relative;

  &:hover {
    background-color: rgba(0, 0, 0, 0.05);
  }
`;

// Tooltip component
const Tooltip = styled.div`
  position: relative;
  display: inline-block;

  &:hover .tooltip-text {
    visibility: visible;
    opacity: 1;
  }
`;

const TooltipText = styled.div`
  visibility: hidden;
  opacity: 0;
  width: max-content;
  background-color: #333;
  color: #fff;
  text-align: center;
  border-radius: 4px;
  padding: 5px 10px;
  position: absolute;
  z-index: 1;
  top: 50%;
  left: 100%;
  margin-left: 10px;
  transform: translateY(-50%);
  transition: opacity 0.3s;
  font-size: 12px;

  &::after {
    content: '';
    position: absolute;
    top: 50%;
    right: 100%;
    margin-top: -5px;
    border-width: 5px;
    border-style: solid;
    border-color: transparent #333 transparent transparent;
  }
`;

// User Avatar component
const UserAvatar = styled.div<{ $imageUrl?: string }>`
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: #e5e7eb;
  background-image: ${props => (props.$imageUrl ? `url(${props.$imageUrl})` : 'none')};
  background-size: cover;
  background-position: center;
  border: 2px solid white;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #6b7280;
  font-weight: 500;
  font-size: 12px;
  cursor: pointer;
`;

const AvatarGroup = styled.div`
  display: flex;
  align-items: center;

  & > ${UserAvatar} {
    margin-right: -12px;
    &:last-child {
      margin-right: 0;
    }
  }
`;

const AdditionalUsersCount = styled.div`
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: #e5e7eb;
  border: 2px solid white;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #6b7280;
  font-weight: 600;
  font-size: 11px;
  margin-left: -12px;
  cursor: pointer;

  &:hover {
    background-color: #d1d5db;
  }
`;

const DateBadge = styled.div`
  font-size: 0.7rem;
  color: #6b7280;
  display: flex;
  align-items: center;
  gap: 4px;
`;

const CardHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 0.5rem;
`;

const StatusBadge = styled.span<{ $color: string }>`
  background-color: ${props => props.$color};
  color: white;
  font-size: 0.7rem;
  padding: 2px 6px;
  border-radius: 100px;
  font-weight: 500;
  display: flex;
  align-items: center;
`;

const MenuButton = styled.button`
  background: none;
  border: none;
  padding: 0;
  font-size: 0.75rem;
  color: #6b7280;
  display: flex;
  align-items: center;
  gap: 0.25rem;
  cursor: pointer;

  &:hover {
    color: #1f2937;
  }
`;

const ProgressBar = styled.div`
  height: 6px;
  background-color: #e5e7eb;
  border-radius: 3px;
  margin: 0.5rem 0;
  overflow: hidden;
`;

const ProgressFill = styled.div<{ $progress: number }>`
  height: 100%;
  width: ${props => props.$progress}%;
  background-color: #10b981;
  border-radius: 3px;
`;

const ProgressText = styled.div`
  font-size: 0.75rem;
  color: #6b7280;
  margin-bottom: 0.5rem;
  display: flex;
  justify-content: space-between;
`;

const ActionButtons = styled.div`
  display: flex;
  gap: 1rem;
  margin-top: 0.5rem;
`;

const ActionButton = styled.div`
  display: flex;
  align-items: center;
  gap: 4px;
  color: #6b7280;
  font-size: 0.75rem;
`;

const PointsBadge = styled.div<{ $isClaimPoint?: boolean }>`
  display: flex;
  align-items: center;
  gap: 4px;
  background-color: ${props => (props.$isClaimPoint ? '#10b981' : '#fef3c7')};
  color: ${props => (props.$isClaimPoint ? '#ffffff' : '#92400e')};
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 0.7rem;
  font-weight: 500;
  margin-left: ${props => (props.$isClaimPoint ? '16px' : '0')};
  position: relative;

  .check-icon {
    position: absolute;
    left: -20px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #10b981;
    border-radius: 50%;
    padding: 2px;
  }

  span {
    line-height: 1;
  }
`;

// Expand/Collapse button for descriptions
const ExpandButton = styled.button`
  background: none;
  border: none;
  color: ${settingsTheme.colors.primary};
  font-size: ${settingsTheme.typography.fontSizes.xs};
  font-weight: ${settingsTheme.typography.fontWeights.medium};
  cursor: pointer;
  padding: ${settingsTheme.spacing.xs} 0;
  margin-top: ${settingsTheme.spacing.xs};
  display: flex;
  align-items: center;
  gap: 0.25rem;
  transition: color 0.2s ease;

  &:hover {
    color: ${settingsTheme.colors.primaryHover};
  }

  &:focus {
    outline: none;
    color: ${settingsTheme.colors.primaryHover};
  }
`;

// Task Card Component
const TaskCard: React.FC<{ task: Task }> = ({ task }) => {
  const router = useRouter();
  const [{ isDragging }, drag] = useDrag<
    { id: number; statusId: number },
    void,
    { isDragging: boolean }
  >(() => ({
    type: 'task',
    item: { id: task.id, statusId: task.statusId },
    collect: monitor => ({
      isDragging: !!monitor.isDragging(),
    }),
  }));

  // State for popover menu
  const [isPopoverOpen, setIsPopoverOpen] = useState(false);
  const [isUsersPopoverOpen, setIsUsersPopoverOpen] = useState(false);
  const popoverRef = useRef<HTMLDivElement>(null);
  const buttonRef = useRef<HTMLButtonElement>(null);
  const usersButtonRef = useRef<HTMLDivElement>(null);
  const usersPopoverRef = useRef<HTMLDivElement>(null);

  // State for description expansion
  const [isDescriptionExpanded, setIsDescriptionExpanded] = useState(false);

  // Check if description needs truncation (rough estimate based on character count)
  const needsTruncation = task.taskDescription && task.taskDescription.length > 120;

  // Format date to display
  const formatDate = (date: Date) => {
    return new Date(date).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
    });
  };

  // Toggle popover menu
  const togglePopover = (e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent triggering drag event
    setIsPopoverOpen(!isPopoverOpen);
  };

  const toggleUsersPopover = (e: React.MouseEvent) => {
    e.stopPropagation();
    setIsUsersPopoverOpen(!isUsersPopoverOpen);
  };

  // Handle clicking outside to close popover
  const handleClickOutside = (event: MouseEvent) => {
    if (
      popoverRef.current &&
      !popoverRef.current.contains(event.target as Node) &&
      buttonRef.current &&
      !buttonRef.current.contains(event.target as Node)
    ) {
      setIsPopoverOpen(false);
    }
    if (
      usersPopoverRef.current &&
      !usersPopoverRef.current.contains(event.target as Node) &&
      usersButtonRef.current &&
      !usersButtonRef.current.contains(event.target as Node)
    ) {
      setIsUsersPopoverOpen(false);
    }
  };

  // Add event listener for clicking outside
  useEffect(() => {
    if (isPopoverOpen || isUsersPopoverOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isPopoverOpen, isUsersPopoverOpen]);

  // Handle view details
  const handleViewDetails = (e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent triggering drag event
    setIsPopoverOpen(false);
    router.push(`/kanban/${task.id}`);
  };

  // Handle edit task
  const handleEdit = (e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent triggering drag event
    setIsPopoverOpen(false);
    router.push(`/kanban/${task.id}/edit`);
  };

  // Handle chat action
  const handleChat = (e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent triggering drag event
    setIsPopoverOpen(false);
    // Store the task info in sessionStorage to be picked up by chat page
    sessionStorage.setItem(
      'openTaskChat',
      JSON.stringify({
        taskId: task.id,
        taskTitle: task.taskTitle,
        timestamp: Date.now(),
      })
    );
    // Navigate to chat page
    router.push('/chat?tab=task');
  };

  // Get initials for avatar fallback
  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(part => part[0])
      .join('')
      .toUpperCase()
      .substring(0, 2);
  };

  return (
    <Card ref={drag as any} style={{ opacity: isDragging ? 0.5 : 1 }}>
      <CardHeader>
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <StatusBadge $color={task.status.color}>{task.status.displayName}</StatusBadge>
        </div>
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <DateBadge>
            <Timer size={12} />
            {task.dueDate ? formatDate(task.dueDate) : formatDate(task.createdAt)}
          </DateBadge>
          <DetailButton
            onClick={togglePopover}
            ref={buttonRef}
            style={{ marginRight: '0', position: 'relative' }}
          >
            <MoreVertical size={14} />
            <PopoverMenu $isOpen={isPopoverOpen} ref={popoverRef}>
              <PopoverItem>
                <div className="btn" onClick={handleViewDetails}>
                  <PopoverIcon>
                    <Info size={12} />
                  </PopoverIcon>
                  Detail
                </div>
              </PopoverItem>
              <PopoverItem>
                <div className="btn" onClick={handleEdit}>
                  <PopoverIcon>
                    <Edit size={12} />
                  </PopoverIcon>
                  Edit
                </div>
              </PopoverItem>
              <PopoverItem>
                <div className="btn" onClick={handleChat}>
                  <PopoverIcon>
                    <MessageSquare size={12} />
                  </PopoverIcon>
                  Chat
                </div>
              </PopoverItem>
            </PopoverMenu>
          </DetailButton>
        </div>
      </CardHeader>

      <CardTitle>{task.taskTitle}</CardTitle>

      {task.taskDescription && (
        <div>
          <CardDescription
            $isExpanded={isDescriptionExpanded}
            data-expanded={isDescriptionExpanded}
            dangerouslySetInnerHTML={{ __html: task.taskDescription }}
          />
          {needsTruncation && (
            <ExpandButton
              onClick={e => {
                e.stopPropagation(); // Prevent triggering drag event
                setIsDescriptionExpanded(!isDescriptionExpanded);
              }}
            >
              {isDescriptionExpanded ? 'Show less' : 'Show more'}
            </ExpandButton>
          )}
        </div>
      )}

      {/* Organization and Department info */}
      <InfoBadgeContainer>
        {task.organization && (
          <InfoBadge>
            <Building size={8} />
            {task.organization.name}
          </InfoBadge>
        )}
        {task.department && (
          <InfoBadge>
            <Briefcase size={8} />
            {task.department.name}
          </InfoBadge>
        )}
      </InfoBadgeContainer>

      <CardFooter>
        <div
          style={{
            display: 'flex',
            justifyContent: 'space-between',
            width: '100%',
            alignItems: 'center',
          }}
        >
          <div style={{ position: 'relative' }}>
            <div ref={usersButtonRef} onClick={toggleUsersPopover} style={{ cursor: 'pointer' }}>
              <AvatarGroup>
                {task.taskAssignments.slice(0, 3).map((assignment, index) => (
                  <Tooltip key={assignment.id}>
                    <UserAvatar $imageUrl={assignment.user.imageUrl || undefined}>
                      {!assignment.user.imageUrl &&
                        getInitials(`${assignment.user.firstName} ${assignment.user.lastName}`)}
                    </UserAvatar>
                    <TooltipText className="tooltip-text">
                      {assignment.user.firstName} {assignment.user.lastName}
                    </TooltipText>
                  </Tooltip>
                ))}
                {task.taskAssignments.length > 3 && (
                  <Tooltip>
                    <AdditionalUsersCount>+{task.taskAssignments.length - 3}</AdditionalUsersCount>
                    <TooltipText className="tooltip-text">
                      {task.taskAssignments
                        .slice(3)
                        .map(a => `${a.user.firstName} ${a.user.lastName}`)
                        .join(', ')}
                    </TooltipText>
                  </Tooltip>
                )}
              </AvatarGroup>
            </div>
            <UsersPopoverMenu ref={usersPopoverRef} $isOpen={isUsersPopoverOpen}>
              <UsersPopoverHeader>
                Assigned Users ({task.taskAssignments.length})
              </UsersPopoverHeader>
              <UsersPopoverContent>
                {task.taskAssignments.map((assignment, index) => (
                  <UserPopoverItem key={assignment.user.id || index}>
                    <UserPopoverAvatar $imageUrl={assignment.user.imageUrl}>
                      {!assignment.user.imageUrl &&
                        getInitials(`${assignment.user.firstName} ${assignment.user.lastName}`)}
                    </UserPopoverAvatar>
                    <UserPopoverInfo>
                      <UserPopoverName>
                        {assignment.user.firstName} {assignment.user.lastName}
                      </UserPopoverName>
                      <UserPopoverEmail>{assignment.user.email}</UserPopoverEmail>
                    </UserPopoverInfo>
                  </UserPopoverItem>
                ))}
              </UsersPopoverContent>
            </UsersPopoverMenu>
          </div>
          {task.points !== undefined && task.points > 0 && (
            <PointsBadge $isClaimPoint={task.isClaimPoint}>
              {task.isClaimPoint && (
                <div className="check-icon">
                  <Check size={12} color="white" />
                </div>
              )}
              <Award size={12} />
              <span>{task.points} pts</span>
            </PointsBadge>
          )}
        </div>
      </CardFooter>
    </Card>
  );
};

// Lane Component
function KanbanLane({
  status,
  tasks,
  onDropTask,
  canDropTask,
}: {
  status: TaskStatus;
  tasks: Task[];
  onDropTask: (taskId: number, statusId: number) => void;
  canDropTask: (taskId: number, fromStatusId: number, toStatusId: number) => boolean;
}): React.ReactElement {
  const [{ isOver, canDrop }, drop] = useDrop<
    { id: number; statusId: number },
    void,
    { isOver: boolean; canDrop: boolean }
  >(() => ({
    accept: 'task',
    canDrop: item => {
      // Don't allow dropping in the same lane
      if (item.statusId === status.id) return false;
      // Check if this transition is allowed
      return canDropTask(item.id, item.statusId, status.id);
    },
    drop: item => onDropTask(item.id, status.id),
    collect: monitor => ({
      isOver: !!monitor.isOver(),
      canDrop: !!monitor.canDrop(),
    }),
  }));

  const laneRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (laneRef.current) {
      drop(laneRef.current);
    }
  }, [drop]);

  // Use the color directly from the status object
  const getLaneIcon = (statusName: string) => {
    switch (statusName.toLowerCase()) {
      case 'to-do':
      case 'to do':
        return <Plus size={14} />;
      case 'in progress':
      case 'on progress':
        return <Star size={14} />;
      case 'need review':
      case 'review':
        return <AlertTriangle size={14} />;
      default:
        return null;
    }
  };

  const badgeColor = status.color;
  const icon = getLaneIcon(status.name);

  return (
    <Lane
      ref={laneRef}
      style={{
        backgroundColor: isOver ? (canDrop ? '#F1F5F9' : '#FEE2E2') : undefined,
        opacity: isOver && !canDrop ? 0.3 : 1,
      }}
    >
      <LaneHeader color={status.color}>
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <LaneTitle>
            {icon}
            {status.displayName}
          </LaneTitle>
          <LaneCount $bgColor={badgeColor}>{tasks.length}</LaneCount>
        </div>
        {/* <MenuButton>
          <MoreVertical size={16} />
        </MenuButton> */}
      </LaneHeader>
      <LaneBody>
        {tasks.length > 0 ? (
          tasks.map(task => <TaskCard key={task.id} task={task} />)
        ) : (
          <EmptyLaneMessage>
            {status.id === 1 ? (
              <>
                <div>ยังไม่มี Task</div>
                <div>Task จะปรากฏที่นี่เมื่อถูกสร้าง</div>
              </>
            ) : (
              <>
                <div>ไม่มี Task ใน {status.displayName}</div>
                <div>ลาก Task มาที่นี่เพื่อเปลี่ยนสถานะ</div>
              </>
            )}
          </EmptyLaneMessage>
        )}
      </LaneBody>
    </Lane>
  );
}

// No mock data - all data will be fetched from API

const FilterContainer = styled.div`
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
  align-items: flex-end;
`;

const FilterSelect = styled.select`
  padding: 0.5rem;
  border-radius: 4px;
  border: 1px solid #e0e0e0;
  font-size: 0.875rem;
  min-width: 200px;
`;

const FilterLabel = styled.label`
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  font-size: 0.75rem;
  color: #666;
`;

const LoadingIndicator = styled.div`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #666;
  font-size: 0.875rem;
  animation: fadeIn 0.3s ease-in-out;

  @keyframes fadeIn {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }

  svg {
    animation: spin 1s linear infinite;
  }

  @keyframes spin {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }
`;

const CreateTaskButton = styled.button`
  background-color: #10b981;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: background-color 0.2s;

  &:hover {
    background-color: #059669;
  }

  /* Tablet */
  @media (max-width: 1023px) {
    padding: 0.45rem 0.875rem;
    font-size: 0.8rem;
    gap: 0.375rem;
  }

  /* Mobile */
  @media (max-width: 767px) {
    padding: 0.5rem 0.75rem;
    font-size: 0.75rem;
    gap: 0.25rem;
    width: 100%;
    justify-content: center;
    min-height: 44px; /* Touch-friendly height */
  }

  /* Small mobile */
  @media (max-width: 480px) {
    padding: 0.5rem;
    font-size: 0.7rem;

    /* Show only icon on very small screens */
    span {
      display: none;
    }
  }
`;

// Helper function to detect touch devices
const isTouchDevice = () => {
  return (
    'ontouchstart' in window ||
    navigator.maxTouchPoints > 0 ||
    // @ts-ignore
    navigator.msMaxTouchPoints > 0
  );
};

// Main Kanban Page Component
export default function KanbanPage() {
  const [statuses, setStatuses] = useState<TaskStatus[]>([]);
  const [tasks, setTasks] = useState<Task[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  const router = useRouter();
  const { getToken } = useAuth();
  const { userData } = useUserStore();
  const emit = useSocketEmit();
  const isOwner = userData?.role?.name === USER_ROLES.OWNER;
  const isAdmin = userData?.role?.name === USER_ROLES.ADMIN || isOwner; // Owner has admin privileges

  // Function to fetch task statuses and tasks from API
  const fetchStatusesAndTasks = async () => {
    try {
      setLoading(true);
      const token = await getToken();
      if (!token) {
        setError('Authentication required');
        return;
      }

      // Fetch task statuses
      const response = await fetch('/api/v1/task-status', {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        throw new Error('Failed to fetch task statuses');
      }

      const data = await response.json();
      setStatuses(data.taskStatuses || []);

      // Fetch tasks after statuses are loaded
      await fetchTasks(token);
    } catch (err) {
      console.error('Error fetching task statuses:', err);
      // Set empty statuses array if API fails
      setStatuses([]);
      // Set empty tasks array
      setTasks([]);
    } finally {
      setLoading(false);
    }
  };

  // Fetch data on component mount
  useEffect(() => {
    fetchStatusesAndTasks();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Filter-based task fetching removed

  // Function to fetch tasks based on user role and filters
  const fetchTasks = async (token: string) => {
    try {
      // Build query parameters based on user role
      const queryParams = new URLSearchParams();

      // Apply role-based filtering
      // if (isOwner || isAdmin) {
      //   queryParams.append('createdByUserId', userData?.id?.toString() || '');
      // } else {
      //   queryParams.append('assignedToUserId', userData?.id?.toString() || '');
      // }

      const url = `/api/v1/task?${queryParams.toString()}`;
      const response = await fetch(url, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        throw new Error('Failed to fetch tasks');
      }

      const data = await response.json();

      // Transform API response to match our Task interface
      const transformedTasks = data.tasks.map((task: any) => ({
        ...task,
        createdByUser: {
          id: task.createdByUser.id,
          name: `${task.createdByUser.firstName} ${task.createdByUser.lastName}`,
          email: task.createdByUser.email,
        },
        // taskAssignments are already in the correct format from the API
        taskAssignments: task.taskAssignments || [],
        organization: task.organization,
        department: task.department,
      }));

      setTasks(transformedTasks);
    } catch (err) {
      console.error('Error fetching tasks:', err);
      // If API fails, set empty tasks array
      setTasks([]);
    }
  };

  // Check if a task can be dropped into a specific status lane
  const canDropTask = (taskId: number, fromStatusId: number, toStatusId: number): boolean => {
    // Find the current task status
    const currentStatus = statuses.find(s => s.id === fromStatusId);
    if (!currentStatus) return false;

    // Find the target status
    const targetStatus = statuses.find(s => s.id === toStatusId);
    if (!targetStatus) return false;

    // Find the original task to check its original status
    const task = tasks.find(t => t.id === taskId);
    if (!task) return false;

    // Get the original status from the database (this is the status before any drag operations)
    const originalTask = task;

    // If trying to drag back to a status that the task has already been in, check the database status
    // This prevents dragging back to a previous status that shouldn't be allowed
    if (originalTask.statusId === fromStatusId) {
      // Check if this transition is allowed based on user role
      const transition = targetStatus.toStatusTransitions?.find(
        t => t.fromStatusId === fromStatusId
      );

      if (!transition) return false;

      // Check permission based on user role
      if (isOwner && transition.allowOwner) return true;
      if (isAdmin && transition.allowAdmin) return true;
      if (!isOwner && !isAdmin && transition.allowMember) return true;

      return false;
    } else {
      // This is a drag operation back to a previous status
      // We need to check if there's a valid transition from the current status to the target status
      const transition = targetStatus.toStatusTransitions?.find(
        t => t.fromStatusId === fromStatusId
      );

      if (!transition) return false;

      // Check permission based on user role
      if (isOwner && transition.allowOwner) return true;
      if (isAdmin && transition.allowAdmin) return true;
      if (!isOwner && !isAdmin && transition.allowMember) return true;

      return false;
    }
  };

  // Helper function to check if a status is a 'complete' status
  const isCompleteStatus = (statusId: number): boolean => {
    const status = statuses.find(s => s.id === statusId);
    if (!status) return false;

    // Check if the status name or display name contains 'complete', 'done', or 'finished'
    const statusName = status.name.toLowerCase();
    const displayName = status.displayName.toLowerCase();

    return (
      statusName.includes('complete') ||
      statusName.includes('done') ||
      statusName.includes('finished') ||
      displayName.includes('complete') ||
      displayName.includes('done') ||
      displayName.includes('finished')
    );
  };

  // Handle dropping a task into a new status lane
  const handleDropTask = async (taskId: number, newStatusId: number) => {
    // Check if this transition is allowed
    const task = tasks.find(t => t.id === taskId);
    if (!task) return;

    // if (!canDropTask(taskId, task.statusId, newStatusId)) {
    //   console.log('This status transition is not allowed');
    //   return;
    // }

    // Optimistically update the UI
    setTasks(prevTasks =>
      prevTasks.map(t =>
        t.id === taskId
          ? {
              ...t,
              statusId: newStatusId,
              status: statuses.find(s => s.id === newStatusId) || t.status,
              updatedAt: new Date(),
              // If moving to complete status, optimistically update isClaimPoint
              ...(isCompleteStatus(newStatusId) && !t.isClaimPoint && t.points
                ? { isClaimPoint: true }
                : {}),
            }
          : t
      )
    );

    try {
      // Update the task status via API
      // The API will automatically send notifications when status changes
      // including task_status_changed and task_completed notifications
      const token = await getToken();
      if (!token) return;

      const response = await fetch(`/api/v1/task`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({
          id: taskId,
          statusId: newStatusId,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to update task status');
      }

      // Emit socket notification for task status update
      emit('send_notification', {
        service: 'task',
        id: taskId,
      });

      // Check if the task is moved to a 'complete' status and has points to claim
      if (isCompleteStatus(newStatusId) && task.points && !task.isClaimPoint) {
        // Only owners and admins can create point transactions
        if (isOwner || isAdmin) {
          // Create point transactions for all assigned users
          const pointsPerUser = Math.floor(task.points / task.taskAssignments.length);
          const remainderPoints = task.points % task.taskAssignments.length;

          const pointTransactionPromises = task.taskAssignments.map(async (assignment, index) => {
            const pointAmount = pointsPerUser + (index === 0 ? remainderPoints : 0); // Give remainder to first user
            const requestBody = {
              taskId: task.id,
              pointAmount,
              userId: assignment.userId,
            };

            console.log('Creating point transaction with data:', requestBody);

            try {
              const pointResponse = await fetch('/api/v1/point-transaction', {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json',
                  Authorization: `Bearer ${token}`,
                },
                body: JSON.stringify(requestBody),
              });

              if (!pointResponse.ok) {
                const errorData = await pointResponse.json().catch(() => ({}));
                console.error('Failed to create point transaction:', {
                  status: pointResponse.status,
                  statusText: pointResponse.statusText,
                  error: errorData,
                });
                // We don't throw here to avoid reverting the status change
                // The user can still claim points later
              } else {
                console.log('Point transaction created successfully');
              }
            } catch (error) {
              console.error('Exception while creating point transaction:', error);
            }
          });

          // Wait for all point transactions to complete
          await Promise.all(pointTransactionPromises);
        } else {
          console.log('Only owners and admins can create point transactions');
          // Non-admin users cannot create point transactions
          // The task status change will still be saved
        }
      }

      // No need to update the state again as we've already done it optimistically
    } catch (err) {
      console.error('Error updating task status:', err);
      // Revert the optimistic update if the API call fails
      await fetchTasks((await getToken()) || '');
    }
  };

  if (loading) {
    // Determine number of skeleton lanes based on user role
    const skeletonLanes = isOwner || isAdmin ? 6 : 5;

    return (
      <KanbanContainer>
        <div>
          {/* Skeleton for Kanban Header */}
          <KanbanHeader>
            <div className="h-8 w-48 bg-gray-200 rounded"></div>
            <div className="h-10 w-36 bg-gray-200 rounded"></div>
          </KanbanHeader>

          {/* Skeleton for Task Summary */}
          <TaskSummary>
            {[...Array(skeletonLanes)].map((_, i) => (
              <div key={i} className="h-6 w-28 bg-gray-200 rounded-full"></div>
            ))}
          </TaskSummary>

          {/* Skeleton for Kanban Board */}
          <KanbanBoard>
            {[...Array(skeletonLanes)].map((_, i) => (
              <Lane key={i}>
                <LaneHeader color="#e2e8f0">
                  <div className="flex items-center gap-2">
                    <div className="h-6 w-28 bg-gray-200 rounded"></div>
                    <div className="h-5 w-8 bg-gray-200 rounded-full"></div>
                  </div>
                  <div className="h-5 w-5 bg-gray-200 rounded"></div>
                </LaneHeader>
                <LaneBody>
                  {[...Array(i === 0 ? 4 : i === 1 ? 3 : 2)].map((_, j) => (
                    <Card key={j}>
                      <div className="h-5 w-4/5 bg-gray-200 rounded mb-2"></div>
                      <div className="h-4 w-full bg-gray-100 rounded mb-2"></div>
                      <div className="h-4 w-3/5 bg-gray-100 rounded mb-3"></div>
                      <div className="flex gap-2 mb-3">
                        <div className="h-5 w-16 bg-gray-200 rounded-full"></div>
                        <div className="h-5 w-20 bg-gray-200 rounded-full"></div>
                      </div>
                      <CardFooter>
                        <div className="flex justify-between items-center">
                          <div className="flex gap-2">
                            <div className="h-6 w-6 bg-gray-200 rounded-full"></div>
                            <div className="h-4 w-20 bg-gray-200 rounded"></div>
                          </div>
                          <div className="flex gap-2">
                            <div className="h-5 w-5 bg-gray-200 rounded-full"></div>
                            <div className="h-5 w-5 bg-gray-200 rounded-full"></div>
                          </div>
                        </div>
                      </CardFooter>
                    </Card>
                  ))}
                </LaneBody>
              </Lane>
            ))}
          </KanbanBoard>
        </div>
      </KanbanContainer>
    );
  }

  if (error) {
    return (
      <KanbanContainer>
        <div>{error}</div>
      </KanbanContainer>
    );
  }

  // Modal and form functionality

  // Filter statuses to only show the three main ones: To-do, In Progress, Need Review
  const mainStatuses = statuses.filter(status => {
    const displayName = status.displayName.toLowerCase();
    return (
      displayName.includes('to do') ||
      displayName.includes('progress') ||
      displayName.includes('review')
    );
  });

  // Choose backend based on device type
  const dndBackend = isTouchDevice() ? TouchBackend : HTML5Backend;
  const backendOptions = isTouchDevice()
    ? {
        enableMouseEvents: true, // Allow mouse events on touch devices
        delayTouchStart: 200, // Add delay to distinguish between scroll and drag
        delayMouseStart: 0,
      }
    : {};

  return (
    <DndProvider backend={dndBackend} options={backendOptions}>
      <KanbanContainer>
        <KanbanHeader>
          <KanbanTitle>บอร์ดงาน</KanbanTitle>
          {/* {isAdmin && ( */}
          <CreateTaskButton onClick={() => router.push('/kanban/create')}>
            <Plus size={16} />
            <span>สร้าง Task</span>
          </CreateTaskButton>
          {/* )} */}
        </KanbanHeader>

        <TaskSummary>
          <SummaryBadge>
            <span className="count">{tasks.length}</span>
            <span className="label">Task ทั้งหมด</span>
          </SummaryBadge>
          {statuses.map(status => {
            const count = tasks.filter(task => task.statusId === status.id).length;
            // Use the color from taskStatus with 20% opacity for the badge background
            const bgColor = `${status.color}20`;

            return (
              <SummaryBadge key={status.id} $bgColor={bgColor}>
                <span className="count">{count}</span>
                <span className="label">{status.displayName}</span>
              </SummaryBadge>
            );
          })}
        </TaskSummary>

        {/* Organization and department filters removed */}

        <KanbanBoard>
          {statuses.map(status => (
            <KanbanLane
              key={status.id}
              status={status}
              tasks={tasks.filter(task => task.statusId === status.id)}
              onDropTask={handleDropTask}
              canDropTask={canDropTask}
            />
          ))}
        </KanbanBoard>
      </KanbanContainer>
    </DndProvider>
  );
}

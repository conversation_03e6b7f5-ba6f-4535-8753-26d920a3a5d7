import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { authenticateUser } from '@/lib/auth';

/**
 * POST API to share feedback with other users
 *
 * This endpoint duplicates a specific FeedbackUser record for multiple users:
 * - Takes feedback_user_id to identify the source FeedbackUser record to duplicate
 * - Takes share_user_ids array of user IDs to share with
 * - Duplicates all data from the source FeedbackUser record
 * - Creates new records for each share_user_id with isShare = true
 * - Preserves all original data (reflection, isAccept, etc.) from source record
 *
 * Body parameters:
 * - feedback_user_id: ID of the FeedbackUser record to duplicate
 * - share_user_ids: Array of user IDs to create shared records for
 */
export async function POST(request: NextRequest) {
  try {
    const auth = await authenticateUser(request);
    if ('error' in auth) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }

    const body = await request.json();
    const { feedback_user_id, share_user_ids } = body;

    // Validate required fields
    if (!feedback_user_id) {
      return NextResponse.json({ error: 'feedback_user_id is required' }, { status: 400 });
    }

    if (!share_user_ids || !Array.isArray(share_user_ids) || share_user_ids.length === 0) {
      return NextResponse.json({ error: 'At least one share_user_id is required' }, { status: 400 });
    }

    // Log the request data for debugging
    console.log('Share feedback request:', {
      feedback_user_id,
      share_user_ids,
      sharedBy: auth.userId,
    });

    const feedbackUserIdNum = Number(feedback_user_id);
    if (isNaN(feedbackUserIdNum)) {
      return NextResponse.json({ error: 'Invalid feedback_user_id' }, { status: 400 });
    }

    // Validate share user IDs
    const shareUserIdNums = share_user_ids.map((id: any) => Number(id)).filter((id: number) => !isNaN(id));
    if (shareUserIdNums.length !== share_user_ids.length) {
      return NextResponse.json({ error: 'Invalid share_user_ids provided' }, { status: 400 });
    }

    // Get the source FeedbackUser record to duplicate
    const sourceFeedbackUser = await prisma.feedbackUser.findUnique({
      where: { id: feedbackUserIdNum },
      include: {
        feedback: true,
      },
    });

    if (!sourceFeedbackUser) {
      return NextResponse.json({ error: 'FeedbackUser record not found' }, { status: 404 });
    }

    // Check if user has permission to share this feedback
    // User must be either the creator of the feedback or the assigned user
    const feedback = sourceFeedbackUser.feedback;
    if (feedback.createFromId !== auth.userId && sourceFeedbackUser.userId !== auth.userId) {
      return NextResponse.json({
        error: 'You do not have permission to share this feedback'
      }, { status: 403 });
    }

    // Check if the share users exist
    const existingUsers = await prisma.user.findMany({
      where: {
        id: { in: shareUserIdNums },
        deletedAt: null,
      },
      select: { id: true },
    });

    if (existingUsers.length !== shareUserIdNums.length) {
      return NextResponse.json({ error: 'Some share users do not exist' }, { status: 400 });
    }

    // Check for existing feedback assignments to avoid duplicates
    const existingAssignments = await prisma.feedbackUser.findMany({
      where: {
        feedbackId: sourceFeedbackUser.feedbackId,
        userId: { in: shareUserIdNums },
      },
      select: { userId: true },
    });

    const existingUserIds = existingAssignments.map(fu => fu.userId);
    const newUserIds = shareUserIdNums.filter(id => !existingUserIds.includes(id));

    if (newUserIds.length === 0) {
      return NextResponse.json({
        error: 'All selected users already have access to this feedback'
      }, { status: 400 });
    }

    // Create shared feedback user records by duplicating the source record
    const sharedFeedbackUsers = await prisma.$transaction(async (tx) => {
      const createdRecords = [];

      for (const userId of newUserIds) {
        // Duplicate all data from source FeedbackUser record
        const sharedFeedbackUser = await tx.feedbackUser.create({
          data: {
            feedbackId: sourceFeedbackUser.feedbackId,
            userId: userId, // The user who will receive the shared feedback
            shareUserId: auth.userId, // Current user who is sharing
            feedbackToUserId: sourceFeedbackUser.feedbackToUserId, // The user this feedback is directed to
            isShare: true, // Mark as shared
            // Duplicate all original data from source record
            isAccept: sourceFeedbackUser.isAccept,
            isDiscard: sourceFeedbackUser.isDiscard,
            reflection: sourceFeedbackUser.reflection,
          },
          include: {
            user: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true,
              },
            },
            shareUser: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true,
              },
            },
          },
        });

        createdRecords.push(sharedFeedbackUser);
      }

      return createdRecords;
    });

    // Emit socket notification for shared feedback
    // Note: This would typically be handled by a socket service
    // For now, we'll just log it
    console.log('Feedback shared:', {
      feedbackId: sourceFeedbackUser.feedbackId,
      sourceFeedbackUserId: feedbackUserIdNum,
      sharedBy: auth.userId,
      sharedWith: newUserIds,
    });

    return NextResponse.json({
      message: `Feedback shared successfully with ${newUserIds.length} user${newUserIds.length > 1 ? 's' : ''}`,
      sharedWith: sharedFeedbackUsers.map(sfu => ({
        id: sfu.id,
        userId: sfu.userId,
        userName: `${sfu.user.firstName} ${sfu.user.lastName}`,
        userEmail: sfu.user.email,
        sharedBy: `${sfu.shareUser?.firstName} ${sfu.shareUser?.lastName}`,
      })),
      skippedUsers: existingUserIds.length > 0 ? existingUserIds : undefined,
      shareContext: {
        sourceFeedbackUserId: feedbackUserIdNum,
        duplicatedFromUser: sourceFeedbackUser.userId,
        sharedBy: auth.userId,
        preservedOriginalData: true,
      },
    });

  } catch (error) {
    console.error('Error sharing feedback:', error);
    return NextResponse.json({ error: 'Failed to share feedback' }, { status: 500 });
  }
}

/**
 * GET API to get shared feedback information
 * 
 * Query parameters:
 * - feedbackId: ID of the feedback to get sharing information for
 */
export async function GET(request: NextRequest) {
  try {
    const auth = await authenticateUser(request);
    if ('error' in auth) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }

    const url = new URL(request.url);
    const feedbackId = url.searchParams.get('feedbackId');

    if (!feedbackId) {
      return NextResponse.json({ error: 'Feedback ID is required' }, { status: 400 });
    }

    const feedbackIdNum = Number(feedbackId);
    if (isNaN(feedbackIdNum)) {
      return NextResponse.json({ error: 'Invalid feedback ID' }, { status: 400 });
    }

    // Get shared feedback information
    const sharedFeedbackUsers = await prisma.feedbackUser.findMany({
      where: {
        feedbackId: feedbackIdNum,
        isShare: true,
      },
      include: {
        user: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
          },
        },
        shareUser: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    return NextResponse.json({
      sharedWith: sharedFeedbackUsers.map(sfu => ({
        id: sfu.id,
        userId: sfu.userId,
        userName: `${sfu.user.firstName} ${sfu.user.lastName}`,
        userEmail: sfu.user.email,
        sharedBy: sfu.shareUser ? `${sfu.shareUser.firstName} ${sfu.shareUser.lastName}` : 'Unknown',
        sharedAt: sfu.createdAt,
        isAccepted: sfu.isAccept,
        isDiscarded: sfu.isDiscard,
        hasReflection: !!sfu.reflection,
      })),
    });

  } catch (error) {
    console.error('Error getting shared feedback information:', error);
    return NextResponse.json({ error: 'Failed to get shared feedback information' }, { status: 500 });
  }
}

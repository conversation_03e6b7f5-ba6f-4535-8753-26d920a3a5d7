import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import bcrypt from 'bcrypt';
import { signJwt } from '@/lib/jwt';
import { USER_ROLES } from '@/constants/roles';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    // Validate required fields
    const { email, password, rememberMe = false } = body;

    if (!email || !password) {
      return NextResponse.json({ error: 'Email and password are required' }, { status: 400 });
    }

    // Find the user by email (excluding deleted users)
    const user = await prisma.user.findUnique({
      where: {
        email,
        deletedAt: null, // Only allow users who haven't been deleted
      },
      include: {
        userRole: true,
      },
    });

    // Check if user exists or has been deleted
    if (!user) {
      return NextResponse.json({ error: 'Invalid email or password' }, { status: 401 });
    }

    // Verify password
    const passwordMatch = await bcrypt.compare(password, user.passwordHash);

    if (!passwordMatch) {
      return NextResponse.json({ error: 'Invalid email or password' }, { status: 401 });
    }

    // Generate JWT token with appropriate expiration
    const expiresIn = rememberMe ? '30d' : '24h';
    const token = signJwt({
      userId: user.id,
      email: user.email,
      role: user.userRole.name,
    }, expiresIn);

    // Get user's organization and department info
    const userOrganization = await prisma.organization.findFirst({
      where: { ownerUserId: user.id },
    });

    const userDepartments = await prisma.departmentMember.findMany({
      where: { userId: user.id },
      include: {
        department: true,
      },
    });

    // Return success response with token and user info
    return NextResponse.json({
      message: 'Login successful',
      token,
      user: {
        id: user.id,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        role: user.userRole.name,
        organization: userOrganization
          ? {
              id: userOrganization.id,
              name: userOrganization.name,
            }
          : null,
        departments: userDepartments.map(membership => ({
          id: membership.department.id,
          name: membership.department.name,
        })),
      },
    });
  } catch (error) {
    console.error('Login error:', error);
    return NextResponse.json({ error: 'Failed to process login' }, { status: 500 });
  }
}

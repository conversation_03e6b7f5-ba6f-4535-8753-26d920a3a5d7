import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { authenticateUser } from '@/lib/auth';
import { 
  canCreateFeedback, 
  canDeleteFeedback, 
  getAssignableFeedbackUsers,
  hasOrganizationAdminPrivileges,
  isAnyDepartmentLeader 
} from '@/lib/permissions';

/**
 * GET API for feedback
 * 
 * This API supports multiple modes:
 * 
 * 1. Get a single feedback by ID:
 *    - Path: /api/v1/feedback?id=123
 *    - Returns: Detailed feedback information
 * 
 * 2. Get user's own feedback (created, assigned, shared):
 *    - Path: /api/v1/feedback?view=my
 *    - Returns: User's feedback categorized by type
 * 
 * 3. Get all feedback (admin/leader view):
 *    - Path: /api/v1/feedback?view=all&organizationId=123
 *    - Path: /api/v1/feedback?view=all&departmentId=123
 *    - Returns: All feedback based on user permissions
 */
export async function GET(request: NextRequest) {
  try {
    const auth = await authenticateUser(request);
    if ('error' in auth) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }

    const url = new URL(request.url);
    const feedbackId = url.searchParams.get('id');
    const view = url.searchParams.get('view');
    const organizationId = url.searchParams.get('organizationId');
    const departmentId = url.searchParams.get('departmentId');

    // Get single feedback by ID
    if (feedbackId) {
      const feedbackIdNum = Number(feedbackId);
      if (isNaN(feedbackIdNum)) {
        return NextResponse.json({ error: 'Invalid feedback ID' }, { status: 400 });
      }

      const feedback = await prisma.feedback.findUnique({
        where: { id: feedbackIdNum },
        include: {
          feedbackType: {
            select: {
              id: true,
              name: true,
              displayName: true,
            },
          },
          createFrom: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
            },
          },
          feedbackUsers: {
            include: {
              user: {
                select: {
                  id: true,
                  firstName: true,
                  lastName: true,
                  email: true,
                },
              },
              shareUser: {
                select: {
                  id: true,
                  firstName: true,
                  lastName: true,
                  email: true,
                },
              },
              feedbackToUser: {
                select: {
                  id: true,
                  firstName: true,
                  lastName: true,
                  email: true,
                },
              },
            },
          },
        },
      });

      if (!feedback) {
        return NextResponse.json({ error: 'Feedback not found' }, { status: 404 });
      }

      // Check if user can view this feedback
      const canView = feedback.createFromId === auth.userId || 
                     feedback.feedbackUsers.some(fu => fu.userId === auth.userId) ||
                     auth.isOwner || auth.isAdmin;

      if (!canView) {
        return NextResponse.json({ error: 'Access denied' }, { status: 403 });
      }

      return NextResponse.json({ feedback });
    }

    // Get user's own feedback
    if (view === 'my') {
      const [createdFeedback, assignedFeedback, sharedFeedback] = await Promise.all([
        // Feedback created by user
        prisma.feedback.findMany({
          where: { createFromId: auth.userId },
          include: {
            feedbackType: {
              select: {
                id: true,
                name: true,
                displayName: true,
              },
            },
            feedbackUsers: {
              include: {
                user: {
                  select: {
                    id: true,
                    firstName: true,
                    lastName: true,
                    email: true,
                  },
                },
                shareUser: {
                  select: {
                    id: true,
                    firstName: true,
                    lastName: true,
                    email: true,
                  },
                },
                feedbackToUser: {
                  select: {
                    id: true,
                    firstName: true,
                    lastName: true,
                    email: true,
                  },
                },
              },
            },
          },
          orderBy: { createdAt: 'desc' },
        }),
        // Feedback assigned to user
        prisma.feedback.findMany({
          where: {
            feedbackUsers: {
              some: {
                userId: auth.userId,
                isShare: false,
              },
            },
          },
          include: {
            feedbackType: {
              select: {
                id: true,
                name: true,
                displayName: true,
              },
            },
            createFrom: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true,
              },
            },
            feedbackUsers: {
              where: {
                userId: auth.userId,
                isShare: false,
              },
              include: {
                user: {
                  select: {
                    id: true,
                    firstName: true,
                    lastName: true,
                    email: true,
                  },
                },
                shareUser: {
                  select: {
                    id: true,
                    firstName: true,
                    lastName: true,
                    email: true,
                  },
                },
                feedbackToUser: {
                  select: {
                    id: true,
                    firstName: true,
                    lastName: true,
                    email: true,
                  },
                },
              },
            },
          },
          orderBy: { createdAt: 'desc' },
        }),
        // Feedback shared with user
        prisma.feedback.findMany({
          where: {
            feedbackUsers: {
              some: { 
                userId: auth.userId,
                isShare: true,
              },
            },
          },
          include: {
            feedbackType: {
              select: {
                id: true,
                name: true,
                displayName: true,
              },
            },
            createFrom: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true,
              },
            },
            feedbackUsers: {
              // where: { userId: auth.userId },
              include: {
                user: {
                  select: {
                    id: true,
                    firstName: true,
                    lastName: true,
                    email: true,
                  },
                },
                shareUser: {
                  select: {
                    id: true,
                    firstName: true,
                    lastName: true,
                    email: true,
                  },
                },
                feedbackToUser: {
                  select: {
                    id: true,
                    firstName: true,
                    lastName: true,
                    email: true,
                  },
                },
              },
            },
          },
          orderBy: { createdAt: 'desc' },
        }),
      ]);

      return NextResponse.json({
        created: createdFeedback,
        assigned: assignedFeedback,
        shared: sharedFeedback,
      });
    }

    // Get all feedback (admin/leader view)
    if (view === 'all') {
      let whereClause: any = {};

      if (organizationId) {
        const orgIdNum = Number(organizationId);
        if (isNaN(orgIdNum)) {
          return NextResponse.json({ error: 'Invalid organization ID' }, { status: 400 });
        }

        // Check if user has admin privileges for this organization
        const hasAdminPrivileges = await hasOrganizationAdminPrivileges(auth.userId, orgIdNum);
        if (!hasAdminPrivileges) {
          return NextResponse.json({ error: 'Access denied' }, { status: 403 });
        }

        // Filter feedback for organization
        whereClause = {
          OR: [
            { feedbackType: { name: 'organization' } },
            { feedbackType: { name: 'department' } },
            { feedbackType: { name: 'private' } },
          ],
        };
      } else if (departmentId) {
        const deptIdNum = Number(departmentId);
        if (isNaN(deptIdNum)) {
          return NextResponse.json({ error: 'Invalid department ID' }, { status: 400 });
        }

        // Check if user is admin or department leader
        const isLeader = await isAnyDepartmentLeader(auth.userId);
        if (!auth.isOwner && !auth.isAdmin && !isLeader) {
          return NextResponse.json({ error: 'Access denied' }, { status: 403 });
        }

        whereClause = {
          feedbackType: { name: 'department' },
        };
      } else {
        return NextResponse.json({ error: 'Organization ID or Department ID is required for all view' }, { status: 400 });
      }

      const allFeedback = await prisma.feedback.findMany({
        where: whereClause,
        include: {
          feedbackType: {
            select: {
              id: true,
              name: true,
              displayName: true,
            },
          },
          createFrom: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
            },
          },
          feedbackUsers: {
            include: {
              user: {
                select: {
                  id: true,
                  firstName: true,
                  lastName: true,
                  email: true,
                },
              },
              shareUser: {
                select: {
                  id: true,
                  firstName: true,
                  lastName: true,
                  email: true,
                },
              },
              feedbackToUser: {
                select: {
                  id: true,
                  firstName: true,
                  lastName: true,
                  email: true,
                },
              },
            },
          },
        },
        orderBy: { createdAt: 'desc' },
      });

      return NextResponse.json({ feedback: allFeedback });
    }

    return NextResponse.json({ error: 'Invalid request parameters' }, { status: 400 });
  } catch (error) {
    console.error('Error fetching feedback:', error);
    return NextResponse.json({ error: 'Failed to fetch feedback' }, { status: 500 });
  }
}

/**
 * POST API to create new feedback
 */
export async function POST(request: NextRequest) {
  try {
    const auth = await authenticateUser(request);
    if ('error' in auth) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }

    const body = await request.json();
    const {
      feedbackTypeId,
      situation,
      behavior,
      impact,
      actionable,
      appreciation,
      growthToken,
      userIds, // Array of user IDs to assign feedback to
      organizationId,
      departmentId,
      taskId,
    } = body;

    // Validate required fields
    if (!feedbackTypeId) {
      return NextResponse.json({ error: 'Feedback type ID is required' }, { status: 400 });
    }

    if (!userIds || !Array.isArray(userIds) || userIds.length === 0) {
      return NextResponse.json({ error: 'At least one user ID is required' }, { status: 400 });
    }

    // Get feedback type
    const feedbackType = await prisma.feedbackType.findUnique({
      where: { id: feedbackTypeId },
    });

    if (!feedbackType) {
      return NextResponse.json({ error: 'Invalid feedback type' }, { status: 400 });
    }

    // Check if user can create this type of feedback
    const canCreate = await canCreateFeedback(auth.userId, feedbackType.name);
    if (!canCreate) {
      return NextResponse.json({ error: 'You do not have permission to create this type of feedback' }, { status: 403 });
    }

    // Get assignable users for validation
    const assignableUsers = await getAssignableFeedbackUsers(
      auth.userId,
      feedbackType.name,
      organizationId,
      departmentId,
      taskId
    );

    // Validate that all userIds are assignable
    const invalidUserIds = userIds.filter(userId => !assignableUsers.includes(userId));
    if (invalidUserIds.length > 0) {
      return NextResponse.json({
        error: `Cannot assign feedback to users: ${invalidUserIds.join(', ')}`
      }, { status: 400 });
    }

    // Create feedback with users in a transaction
    const result = await prisma.$transaction(async (tx) => {
      // Create feedback
      const feedback = await tx.feedback.create({
        data: {
          feedbackTypeId,
          createFromId: auth.userId,
          situation,
          behavior,
          impact,
          actionable,
          appreciation,
          growthToken,
        },
      });

      // Create feedback users
      const feedbackUsers = await Promise.all(
        userIds.map(userId =>
          tx.feedbackUser.create({
            data: {
              feedbackId: feedback.id,
              userId,
              feedbackToUserId: userId,
            },
          })
        )
      );

      return { feedback, feedbackUsers };
    });

    return NextResponse.json({
      message: 'Feedback created successfully',
      feedback: result.feedback,
      assignedUsers: result.feedbackUsers.length,
    }, { status: 201 });
  } catch (error) {
    console.error('Error creating feedback:', error);
    return NextResponse.json({ error: 'Failed to create feedback' }, { status: 500 });
  }
}

/**
 * PUT API to update existing feedback
 */
export async function PUT(request: NextRequest) {
  try {
    const auth = await authenticateUser(request);
    if ('error' in auth) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }

    const body = await request.json();
    const {
      id,
      situation,
      behavior,
      impact,
      actionable,
      appreciation,
      growthToken,
      userIds, // Array of user IDs to assign feedback to (optional, for updating assignments)
      organizationId,
      departmentId,
      taskId,
    } = body;

    // Validate required fields
    if (!id) {
      return NextResponse.json({ error: 'Feedback ID is required' }, { status: 400 });
    }

    // Get existing feedback
    const existingFeedback = await prisma.feedback.findUnique({
      where: { id },
      include: {
        feedbackType: true,
        feedbackUsers: true,
      },
    });

    if (!existingFeedback) {
      return NextResponse.json({ error: 'Feedback not found' }, { status: 404 });
    }

    // Check if user can update this feedback (only creator can update)
    if (existingFeedback.createFromId !== auth.userId) {
      return NextResponse.json({ error: 'You can only update feedback you created' }, { status: 403 });
    }

    // Update feedback in a transaction
    const result = await prisma.$transaction(async (tx) => {
      // Update feedback
      const updatedFeedback = await tx.feedback.update({
        where: { id },
        data: {
          situation,
          behavior,
          impact,
          actionable,
          appreciation,
          growthToken,
        },
      });

      // Update user assignments if provided
      if (userIds && Array.isArray(userIds)) {
        // Check permissions for user assignment updates
        const canCreate = await canCreateFeedback(auth.userId, existingFeedback.feedbackType.name);
        if (!canCreate) {
          throw new Error('You do not have permission to update user assignments for this feedback type');
        }

        // Get assignable users for validation
        const assignableUsers = await getAssignableFeedbackUsers(
          auth.userId,
          existingFeedback.feedbackType.name,
          organizationId,
          departmentId,
          taskId
        );

        // Validate that all userIds are assignable
        const invalidUserIds = userIds.filter(userId => !assignableUsers.includes(userId));
        if (invalidUserIds.length > 0) {
          throw new Error(`Cannot assign feedback to users: ${invalidUserIds.join(', ')}`);
        }

        // Remove existing assignments
        await tx.feedbackUser.deleteMany({
          where: { feedbackId: id },
        });

        // Create new assignments
        await Promise.all(
          userIds.map(userId =>
            tx.feedbackUser.create({
              data: {
                feedbackId: id,
                userId,
                feedbackToUserId: userId,
              },
            })
          )
        );
      }

      return updatedFeedback;
    });

    return NextResponse.json({
      message: 'Feedback updated successfully',
      feedback: result,
    });
  } catch (error) {
    console.error('Error updating feedback:', error);
    const errorMessage = error instanceof Error ? error.message : 'Failed to update feedback';
    return NextResponse.json({ error: errorMessage }, { status: 500 });
  }
}

/**
 * DELETE API to delete feedback
 */
export async function DELETE(request: NextRequest) {
  try {
    const auth = await authenticateUser(request);
    if ('error' in auth) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }

    const url = new URL(request.url);
    const feedbackId = url.searchParams.get('id');

    if (!feedbackId) {
      return NextResponse.json({ error: 'Feedback ID is required' }, { status: 400 });
    }

    const feedbackIdNum = Number(feedbackId);
    if (isNaN(feedbackIdNum)) {
      return NextResponse.json({ error: 'Invalid feedback ID' }, { status: 400 });
    }

    // Check if user can delete this feedback
    const canDelete = await canDeleteFeedback(auth.userId, feedbackIdNum);
    if (!canDelete) {
      return NextResponse.json({
        error: 'You can only delete feedback you created or you must be an owner'
      }, { status: 403 });
    }

    // Delete feedback (cascade will delete feedback_users)
    await prisma.feedback.delete({
      where: { id: feedbackIdNum },
    });

    return NextResponse.json({
      message: 'Feedback deleted successfully',
    });
  } catch (error) {
    console.error('Error deleting feedback:', error);
    return NextResponse.json({ error: 'Failed to delete feedback' }, { status: 500 });
  }
}

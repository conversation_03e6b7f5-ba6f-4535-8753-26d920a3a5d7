import { NextRequest, NextResponse } from 'next/server';
import { authenticateUser } from '@/lib/auth-utils';
import { prisma } from '@/lib/prisma';
import socketServerService from '@/lib/socket-server';

/**
 * POST API to mark messages as read
 *
 * This API supports two modes:
 *
 * 1. Mark a single message as read:
 *    - Request body: { "messageId": 123 }
 *    - Marks the specified message as read for the current user
 *
 * 2. Mark all messages in a chat as read:
 *    - Request body: { "chatId": 123 }
 *    - Marks all messages in the chat as read for the current user
 *    - Optionally include "beforeMessageId" to mark only messages before a specific message
 */
export async function POST(request: NextRequest) {
  try {
    const auth = await authenticateUser(request);
    if ('error' in auth) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }

    const body = await request.json();
    const { messageId, chatId, beforeMessageId } = body;

    // Validate that either messageId or chatId is provided
    if (!messageId && !chatId) {
      return NextResponse.json(
        { error: 'Either messageId or chatId is required' },
        { status: 400 }
      );
    }

    if (messageId && chatId) {
      return NextResponse.json(
        { error: 'Cannot specify both messageId and chatId' },
        { status: 400 }
      );
    }

    // Mode 1: Mark a single message as read
    if (messageId) {
      const messageIdNum = Number(messageId);
      if (isNaN(messageIdNum)) {
        return NextResponse.json({ error: 'Invalid message ID' }, { status: 400 });
      }

      // Check if message exists and user has access to it
      const message = await prisma.chatMessage.findUnique({
        where: { id: messageIdNum },
        include: {
          chat: {
            include: {
              chatUsers: {
                where: { userId: auth.userId },
              },
            },
          },
        },
      });

      if (!message) {
        return NextResponse.json({ error: 'Message not found' }, { status: 404 });
      }

      if (message.chat.chatUsers.length === 0) {
        return NextResponse.json(
          { error: 'You are not a participant in this chat' },
          { status: 403 }
        );
      }

      // Don't mark own messages as read
      if (message.userId === auth.userId) {
        return NextResponse.json(
          { error: 'Cannot mark your own message as read' },
          { status: 400 }
        );
      }

      // Create or update the read record
      const messageRead = await prisma.messageRead.upsert({
        where: {
          messageId_userId: {
            messageId: messageIdNum,
            userId: auth.userId,
          },
        },
        update: {
          readAt: new Date(),
        },
        create: {
          messageId: messageIdNum,
          userId: auth.userId,
          readAt: new Date(),
        },
      });

      // Emit socket event for real-time updates
      try {
        socketServerService.emitNotification('messages_read', message.chatId, 'UPDATE');
      } catch (socketError) {
        console.error('Failed to emit socket notification:', socketError);
        // Don't fail the request if socket emission fails
      }

      return NextResponse.json({
        message: 'Message marked as read',
        messageRead,
      });
    }

    // Mode 2: Mark all messages in a chat as read
    if (chatId) {
      const chatIdNum = Number(chatId);
      if (isNaN(chatIdNum)) {
        return NextResponse.json({ error: 'Invalid chat ID' }, { status: 400 });
      }

      // Check if user is a participant in the chat
      const chatUser = await prisma.chatUser.findUnique({
        where: {
          chatId_userId: {
            chatId: chatIdNum,
            userId: auth.userId,
          },
        },
      });

      if (!chatUser) {
        return NextResponse.json(
          { error: 'You are not a participant in this chat' },
          { status: 403 }
        );
      }

      // Build the where clause for messages to mark as read
      const messageWhere: any = {
        chatId: chatIdNum,
        userId: { not: auth.userId }, // Don't mark own messages as read
      };

      // If beforeMessageId is specified, only mark messages before that message
      if (beforeMessageId) {
        const beforeMessageIdNum = Number(beforeMessageId);
        if (isNaN(beforeMessageIdNum)) {
          return NextResponse.json({ error: 'Invalid beforeMessageId' }, { status: 400 });
        }
        messageWhere.id = { lte: beforeMessageIdNum };
      }

      // Get all messages that need to be marked as read
      const messages = await prisma.chatMessage.findMany({
        where: messageWhere,
        select: { id: true },
      });

      if (messages.length === 0) {
        return NextResponse.json({
          message: 'No messages to mark as read',
          markedCount: 0,
        });
      }

      // Create read records for all messages (using createMany with skipDuplicates)
      const messageReads = messages.map(message => ({
        messageId: message.id,
        userId: auth.userId,
        readAt: new Date(),
      }));

      const result = await prisma.messageRead.createMany({
        data: messageReads,
        skipDuplicates: true,
      });

      // Emit socket event for real-time updates
      try {
        socketServerService.emitNotification('messages_read', chatIdNum, 'UPDATE');
      } catch (socketError) {
        console.error('Failed to emit socket notification:', socketError);
        // Don't fail the request if socket emission fails
      }

      return NextResponse.json({
        message: 'Messages marked as read',
        markedCount: result.count,
        totalMessages: messages.length,
      });
    }
  } catch (error) {
    console.error('Error marking messages as read:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

/**
 * DELETE API to mark messages as unread
 *
 * This API supports marking a single message as unread:
 * - Request body: { "messageId": 123 }
 * - Removes the read record for the specified message and current user
 */
export async function DELETE(request: NextRequest) {
  try {
    const auth = await authenticateUser(request);
    if ('error' in auth) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }

    const body = await request.json();
    const { messageId } = body;

    if (!messageId) {
      return NextResponse.json({ error: 'Message ID is required' }, { status: 400 });
    }

    const messageIdNum = Number(messageId);
    if (isNaN(messageIdNum)) {
      return NextResponse.json({ error: 'Invalid message ID' }, { status: 400 });
    }

    // Check if message exists and user has access to it
    const message = await prisma.chatMessage.findUnique({
      where: { id: messageIdNum },
      include: {
        chat: {
          include: {
            chatUsers: {
              where: { userId: auth.userId },
            },
          },
        },
      },
    });

    if (!message) {
      return NextResponse.json({ error: 'Message not found' }, { status: 404 });
    }

    if (message.chat.chatUsers.length === 0) {
      return NextResponse.json(
        { error: 'You are not a participant in this chat' },
        { status: 403 }
      );
    }

    // Delete the read record
    const deletedRecord = await prisma.messageRead.deleteMany({
      where: {
        messageId: messageIdNum,
        userId: auth.userId,
      },
    });

    return NextResponse.json({
      message: 'Message marked as unread',
      deletedCount: deletedRecord.count,
    });
  } catch (error) {
    console.error('Error marking message as unread:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

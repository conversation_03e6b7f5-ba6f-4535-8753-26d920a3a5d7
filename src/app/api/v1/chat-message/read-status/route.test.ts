/**
 * Test file for the read-status API endpoint
 * 
 * This file contains tests for:
 * - Getting read status for a single message
 * - Getting read status for multiple messages
 * - Getting read status for all messages in a chat
 * - Error handling and validation
 */

import { NextRequest } from 'next/server';
import { GET } from './route';
import { prisma } from '@/lib/prisma';
import { verifyJwt } from '@/lib/jwt';

// Mock dependencies
jest.mock('@/lib/prisma', () => ({
  prisma: {
    chatMessage: {
      findUnique: jest.fn(),
      findMany: jest.fn(),
      count: jest.fn(),
    },
    chatUser: {
      findFirst: jest.fn(),
      findMany: jest.fn(),
    },
    messageRead: {
      findMany: jest.fn(),
    },
  },
}));

jest.mock('@/lib/jwt', () => ({
  verifyJwt: jest.fn(),
}));

const mockPrisma = prisma as jest.Mocked<typeof prisma>;
const mockVerifyJwt = verifyJwt as jest.MockedFunction<typeof verifyJwt>;

describe('/api/v1/chat-message/read-status', () => {
  const mockUser = {
    id: 1,
    firstName: 'John',
    lastName: 'Doe',
    imageUrl: 'https://example.com/avatar.jpg',
  };

  const mockMessage = {
    id: 1,
    chatId: 1,
    userId: 2,
    content: 'Test message',
    createdAt: new Date(),
    chat: {
      chatUsers: [{ userId: 1 }],
    },
  };

  const mockReadStatus = [
    {
      messageId: 1,
      userId: 1,
      readAt: new Date(),
      user: mockUser,
    },
  ];

  const mockChatParticipants = [
    {
      userId: 1,
      user: mockUser,
    },
    {
      userId: 2,
      user: {
        id: 2,
        firstName: 'Jane',
        lastName: 'Smith',
        imageUrl: null,
      },
    },
  ];

  beforeEach(() => {
    jest.clearAllMocks();
    mockVerifyJwt.mockResolvedValue({ userId: 1, isAdmin: false, isOwner: false });
  });

  describe('GET - Single message read status', () => {
    it('should return read status for a single message', async () => {
      const url = 'http://localhost:3000/api/v1/chat-message/read-status?messageId=1';
      const request = new NextRequest(url, {
        headers: { Authorization: 'Bearer valid-token' },
      });

      mockPrisma.chatMessage.findUnique.mockResolvedValue(mockMessage as any);
      mockPrisma.messageRead.findMany.mockResolvedValue(mockReadStatus as any);
      mockPrisma.chatUser.findMany.mockResolvedValue(mockChatParticipants as any);

      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data).toHaveProperty('messageId', 1);
      expect(data).toHaveProperty('readByUsers');
      expect(data).toHaveProperty('unreadUsers');
      expect(data).toHaveProperty('readCount');
      expect(data).toHaveProperty('unreadCount');
      expect(data.readByUsers).toHaveLength(1);
      expect(data.readByUsers[0].user.firstName).toBe('John');
    });

    it('should return 404 for non-existent message', async () => {
      const url = 'http://localhost:3000/api/v1/chat-message/read-status?messageId=999';
      const request = new NextRequest(url, {
        headers: { Authorization: 'Bearer valid-token' },
      });

      mockPrisma.chatMessage.findUnique.mockResolvedValue(null);

      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(404);
      expect(data.error).toBe('Message not found');
    });

    it('should return 403 for unauthorized access', async () => {
      const url = 'http://localhost:3000/api/v1/chat-message/read-status?messageId=1';
      const request = new NextRequest(url, {
        headers: { Authorization: 'Bearer valid-token' },
      });

      const messageWithoutAccess = {
        ...mockMessage,
        chat: { chatUsers: [] },
      };

      mockPrisma.chatMessage.findUnique.mockResolvedValue(messageWithoutAccess as any);

      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(403);
      expect(data.error).toBe('You are not a participant in this chat');
    });
  });

  describe('GET - Multiple messages read status', () => {
    it('should return read status for multiple messages', async () => {
      const url = 'http://localhost:3000/api/v1/chat-message/read-status?messageIds=1,2,3';
      const request = new NextRequest(url, {
        headers: { Authorization: 'Bearer valid-token' },
      });

      const mockMessages = [
        { ...mockMessage, id: 1 },
        { ...mockMessage, id: 2 },
        { ...mockMessage, id: 3 },
      ];

      mockPrisma.chatMessage.findMany.mockResolvedValue(mockMessages as any);
      mockPrisma.messageRead.findMany.mockResolvedValue([
        { ...mockReadStatus[0], messageId: 1 },
        { ...mockReadStatus[0], messageId: 2 },
      ] as any);
      mockPrisma.chatUser.findMany.mockResolvedValue(mockChatParticipants as any);

      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data).toHaveProperty('messageReadStatuses');
      expect(data.messageReadStatuses).toHaveLength(3);
      expect(data.messageReadStatuses[0]).toHaveProperty('messageId', 1);
      expect(data.messageReadStatuses[0]).toHaveProperty('readByUsers');
      expect(data.messageReadStatuses[0]).toHaveProperty('unreadUsers');
    });

    it('should handle invalid message IDs', async () => {
      const url = 'http://localhost:3000/api/v1/chat-message/read-status?messageIds=invalid,abc,xyz';
      const request = new NextRequest(url, {
        headers: { Authorization: 'Bearer valid-token' },
      });

      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toBe('Invalid message IDs');
    });
  });

  describe('GET - Chat read status', () => {
    it('should return read status for all messages in a chat', async () => {
      const url = 'http://localhost:3000/api/v1/chat-message/read-status?chatId=1';
      const request = new NextRequest(url, {
        headers: { Authorization: 'Bearer valid-token' },
      });

      mockPrisma.chatUser.findFirst.mockResolvedValue({ userId: 1, chatId: 1 } as any);
      mockPrisma.chatMessage.findMany.mockResolvedValue([mockMessage] as any);
      mockPrisma.messageRead.findMany.mockResolvedValue(mockReadStatus as any);
      mockPrisma.chatUser.findMany.mockResolvedValue(mockChatParticipants as any);

      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data).toHaveProperty('messageReadStatuses');
      expect(data).toHaveProperty('pagination');
      expect(data.messageReadStatuses).toHaveLength(1);
      expect(data.pagination).toHaveProperty('page', 1);
      expect(data.pagination).toHaveProperty('limit', 50);
    });

    it('should return 403 for unauthorized chat access', async () => {
      const url = 'http://localhost:3000/api/v1/chat-message/read-status?chatId=1';
      const request = new NextRequest(url, {
        headers: { Authorization: 'Bearer valid-token' },
      });

      mockPrisma.chatUser.findFirst.mockResolvedValue(null);

      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(403);
      expect(data.error).toBe('You do not have access to this chat');
    });
  });

  describe('GET - Error handling', () => {
    it('should return 400 when no parameters are provided', async () => {
      const url = 'http://localhost:3000/api/v1/chat-message/read-status';
      const request = new NextRequest(url, {
        headers: { Authorization: 'Bearer valid-token' },
      });

      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toBe('Please specify messageId, messageIds, or chatId parameter');
    });

    it('should return 401 for invalid token', async () => {
      const url = 'http://localhost:3000/api/v1/chat-message/read-status?messageId=1';
      const request = new NextRequest(url, {
        headers: { Authorization: 'Bearer invalid-token' },
      });

      mockVerifyJwt.mockRejectedValue(new Error('Invalid token'));

      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(500);
      expect(data.error).toBe('Internal server error');
    });

    it('should handle database errors gracefully', async () => {
      const url = 'http://localhost:3000/api/v1/chat-message/read-status?messageId=1';
      const request = new NextRequest(url, {
        headers: { Authorization: 'Bearer valid-token' },
      });

      mockPrisma.chatMessage.findUnique.mockRejectedValue(new Error('Database error'));

      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(500);
      expect(data.error).toBe('Internal server error');
    });
  });
});

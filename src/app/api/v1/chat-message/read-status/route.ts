import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { authenticateUser } from '@/lib/auth';

/**
 * GET API to get detailed read status for messages
 *
 * This API supports multiple modes:
 *
 * 1. Get read status for a single message:
 *    - Path: /api/v1/chat-message/read-status?messageId=123
 *    - Returns: Detailed read status for the specific message
 *
 * 2. Get read status for multiple messages:
 *    - Path: /api/v1/chat-message/read-status?messageIds=123,456,789
 *    - Returns: Read status for all specified messages
 *
 * 3. Get read status for all messages in a chat:
 *    - Path: /api/v1/chat-message/read-status?chatId=123
 *    - Optional query parameters:
 *      - limit: Number of messages to get read status for (default: 50, max: 100)
 *      - page: Page number (default: 1)
 *    - Returns: Read status for messages in the chat
 */
export async function GET(request: NextRequest) {
  try {
    const auth = await authenticateUser(request);
    if ('error' in auth) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }

    const { searchParams } = new URL(request.url);
    const messageId = searchParams.get('messageId');
    const messageIds = searchParams.get('messageIds');
    const chatId = searchParams.get('chatId');
    const limit = Math.min(parseInt(searchParams.get('limit') || '50'), 100);
    const page = parseInt(searchParams.get('page') || '1');

    // Mode 1: Get read status for a single message
    if (messageId) {
      const messageIdNum = Number(messageId);
      if (isNaN(messageIdNum)) {
        return NextResponse.json({ error: 'Invalid message ID' }, { status: 400 });
      }

      // Check if message exists and user has access to it
      const message = await prisma.chatMessage.findUnique({
        where: { id: messageIdNum },
        include: {
          chat: {
            include: {
              chatUsers: {
                where: { userId: auth.userId },
              },
            },
          },
        },
      });

      if (!message) {
        return NextResponse.json({ error: 'Message not found' }, { status: 404 });
      }

      if (message.chat.chatUsers.length === 0) {
        return NextResponse.json(
          { error: 'You are not a participant in this chat' },
          { status: 403 }
        );
      }

      // Get detailed read status
      const readStatus = await prisma.messageRead.findMany({
        where: { messageId: messageIdNum },
        include: {
          user: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              imageUrl: true,
            },
          },
        },
        orderBy: { readAt: 'asc' },
      });

      // Get all chat participants for comparison
      const chatParticipants = await prisma.chatUser.findMany({
        where: { chatId: message.chatId },
        include: {
          user: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              imageUrl: true,
            },
          },
        },
      });

      const readByUsers = readStatus.map(read => ({
        user: read.user,
        readAt: read.readAt,
      }));

      const unreadUsers = chatParticipants
        .filter(participant => 
          participant.userId !== message.userId && // Exclude message sender
          !readStatus.some(read => read.userId === participant.userId)
        )
        .map(participant => participant.user);

      return NextResponse.json({
        messageId: messageIdNum,
        readByUsers,
        unreadUsers,
        totalParticipants: chatParticipants.length - 1, // Exclude sender
        readCount: readByUsers.length,
        unreadCount: unreadUsers.length,
      });
    }

    // Mode 2: Get read status for multiple messages
    if (messageIds) {
      const messageIdArray = messageIds.split(',').map(id => Number(id.trim())).filter(id => !isNaN(id));
      
      if (messageIdArray.length === 0) {
        return NextResponse.json({ error: 'Invalid message IDs' }, { status: 400 });
      }

      // Check if user has access to these messages
      const messages = await prisma.chatMessage.findMany({
        where: { 
          id: { in: messageIdArray },
        },
        include: {
          chat: {
            include: {
              chatUsers: {
                where: { userId: auth.userId },
              },
            },
          },
        },
      });

      // Filter messages user has access to
      const accessibleMessages = messages.filter(message => message.chat.chatUsers.length > 0);
      const accessibleMessageIds = accessibleMessages.map(m => m.id);

      if (accessibleMessageIds.length === 0) {
        return NextResponse.json({ error: 'No accessible messages found' }, { status: 403 });
      }

      // Get read status for all accessible messages
      const readStatuses = await prisma.messageRead.findMany({
        where: { messageId: { in: accessibleMessageIds } },
        include: {
          user: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              imageUrl: true,
            },
          },
        },
        orderBy: [{ messageId: 'asc' }, { readAt: 'asc' }],
      });

      // Group read statuses by message ID
      const readStatusByMessage = readStatuses.reduce((acc, read) => {
        if (!acc[read.messageId]) {
          acc[read.messageId] = [];
        }
        acc[read.messageId].push({
          user: read.user,
          readAt: read.readAt,
        });
        return acc;
      }, {} as Record<number, Array<{ user: any; readAt: Date }>>);

      // Get chat participants for each unique chat
      const uniqueChatIds = [...new Set(accessibleMessages.map(m => m.chatId))];
      const chatParticipants = await Promise.all(
        uniqueChatIds.map(async (chatId) => {
          const participants = await prisma.chatUser.findMany({
            where: { chatId },
            include: {
              user: {
                select: {
                  id: true,
                  firstName: true,
                  lastName: true,
                  imageUrl: true,
                },
              },
            },
          });
          return { chatId, participants };
        })
      );

      const participantsByChatId = chatParticipants.reduce((acc, { chatId, participants }) => {
        acc[chatId] = participants;
        return acc;
      }, {} as Record<number, any[]>);

      // Build response for each message
      const messageReadStatuses = accessibleMessages.map(message => {
        const readByUsers = readStatusByMessage[message.id] || [];
        const chatParticipantsForMessage = participantsByChatId[message.chatId] || [];
        
        const unreadUsers = chatParticipantsForMessage
          .filter(participant => 
            participant.userId !== message.userId && // Exclude message sender
            !readByUsers.some(read => read.user.id === participant.userId)
          )
          .map(participant => participant.user);

        return {
          messageId: message.id,
          readByUsers,
          unreadUsers,
          totalParticipants: chatParticipantsForMessage.length - 1, // Exclude sender
          readCount: readByUsers.length,
          unreadCount: unreadUsers.length,
        };
      });

      return NextResponse.json({ messageReadStatuses });
    }

    // Mode 3: Get read status for all messages in a chat
    if (chatId) {
      const chatIdNum = Number(chatId);
      if (isNaN(chatIdNum)) {
        return NextResponse.json({ error: 'Invalid chat ID' }, { status: 400 });
      }

      // Check if user has access to the chat
      const hasAccess = await prisma.chatUser.findFirst({
        where: {
          chatId: chatIdNum,
          userId: auth.userId,
        },
      });

      if (!hasAccess) {
        return NextResponse.json({ error: 'You do not have access to this chat' }, { status: 403 });
      }

      // Get messages from the chat
      const messages = await prisma.chatMessage.findMany({
        where: { chatId: chatIdNum },
        orderBy: { createdAt: 'desc' },
        take: limit,
        skip: (page - 1) * limit,
      });

      const messageIds = messages.map(m => m.id);

      if (messageIds.length === 0) {
        return NextResponse.json({ messageReadStatuses: [] });
      }

      // Get read status for all messages
      const readStatuses = await prisma.messageRead.findMany({
        where: { messageId: { in: messageIds } },
        include: {
          user: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              imageUrl: true,
            },
          },
        },
        orderBy: [{ messageId: 'asc' }, { readAt: 'asc' }],
      });

      // Get chat participants
      const chatParticipants = await prisma.chatUser.findMany({
        where: { chatId: chatIdNum },
        include: {
          user: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              imageUrl: true,
            },
          },
        },
      });

      // Group read statuses by message ID
      const readStatusByMessage = readStatuses.reduce((acc, read) => {
        if (!acc[read.messageId]) {
          acc[read.messageId] = [];
        }
        acc[read.messageId].push({
          user: read.user,
          readAt: read.readAt,
        });
        return acc;
      }, {} as Record<number, Array<{ user: any; readAt: Date }>>);

      // Build response for each message
      const messageReadStatuses = messages.map(message => {
        const readByUsers = readStatusByMessage[message.id] || [];
        
        const unreadUsers = chatParticipants
          .filter(participant => 
            participant.userId !== message.userId && // Exclude message sender
            !readByUsers.some(read => read.user.id === participant.userId)
          )
          .map(participant => participant.user);

        return {
          messageId: message.id,
          readByUsers,
          unreadUsers,
          totalParticipants: chatParticipants.length - 1, // Exclude sender
          readCount: readByUsers.length,
          unreadCount: unreadUsers.length,
        };
      });

      return NextResponse.json({ 
        messageReadStatuses,
        pagination: {
          page,
          limit,
          hasMore: messages.length === limit,
        },
      });
    }

    return NextResponse.json({ error: 'Please specify messageId, messageIds, or chatId parameter' }, { status: 400 });
  } catch (error) {
    console.error('Error fetching message read status:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

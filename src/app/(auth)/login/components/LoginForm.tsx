'use client';

import React, { useState } from 'react';
import styled from 'styled-components';
import { useRouter } from 'next/navigation';
import { setCookie } from 'cookies-next';
import { Button } from '@/components/ui/Button';
import { useForm } from 'react-hook-form';
import { Mail, Lock } from 'lucide-react';

// Types
interface LoginFormProps {
  onSubmit?: (data: LoginFormData) => void;
  isLoading?: boolean;
  error?: string | null;
}

interface LoginFormData {
  email: string;
  password: string;
  rememberMe: boolean;
}

// Styled Components
const FormContainer = styled.div`
  width: 100%;
`;

const FormHeader = styled.div`
  margin-bottom: 2rem;

  /* Tablet */
  @media (max-width: 1023px) {
    margin-bottom: 1.5rem;
  }

  /* Mobile */
  @media (max-width: 767px) {
    margin-bottom: 1.25rem;
  }
`;

const Title = styled.h1`
  font-size: 1.75rem;
  font-weight: 600;
  color: #111827;
  margin-bottom: 0.75rem;

  /* Tablet */
  @media (max-width: 1023px) {
    font-size: 1.5rem;
  }

  /* Mobile */
  @media (max-width: 767px) {
    font-size: 1.375rem;
    text-align: center;
  }

  /* Small mobile */
  @media (max-width: 480px) {
    font-size: 1.25rem;
  }
`;

const Subtitle = styled.p`
  color: #6b7280;
  font-size: 0.9rem;
  margin-bottom: 1.5rem;

  /* Tablet */
  @media (max-width: 1023px) {
    font-size: 0.875rem;
    margin-bottom: 1.25rem;
  }

  /* Mobile */
  @media (max-width: 767px) {
    font-size: 0.8rem;
    margin-bottom: 1rem;
    text-align: center;
  }
`;

const Form = styled.form`
  display: flex;
  flex-direction: column;
  gap: 1.5rem;

  /* Tablet */
  @media (max-width: 1023px) {
    gap: 1.25rem;
  }

  /* Mobile */
  @media (max-width: 767px) {
    gap: 1rem;
  }
`;

const FormGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: 0.5rem;

  /* Mobile */
  @media (max-width: 767px) {
    gap: 0.375rem;
  }
`;

const Label = styled.label`
  font-size: 0.9rem;
  color: #4b5563;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 0.5rem;

  /* Mobile */
  @media (max-width: 767px) {
    font-size: 0.875rem;
    gap: 0.375rem;
  }
`;

const InputWrapper = styled.div`
  position: relative;
`;

const IconWrapper = styled.div`
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #6b7280;

  /* Tablet */
  @media (max-width: 1023px) {
    left: 10px;
  }

  /* Mobile */
  @media (max-width: 767px) {
    left: 8px;
  }

  /* Small mobile */
  @media (max-width: 480px) {
    left: 6px;
  }

  /* Very small mobile */
  @media (max-width: 360px) {
    left: 4px;
  }
`;

const Input = styled.input`
  width: 100%;
  padding: 0.75rem 0.75rem 0.75rem 2.5rem;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  font-size: 1rem;
  color: #111827;
  background-color: #f9fafb;
  transition: all 0.2s ease;

  &:focus {
    outline: none;
    border-color: #4f46e5;
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.2);
    background-color: white;
  }

  &::placeholder {
    color: #9ca3af;
  }

  /* Tablet */
  @media (max-width: 1023px) {
    padding: 0.625rem 0.625rem 0.625rem 2.25rem;
    font-size: 0.95rem;
  }

  /* Mobile */
  @media (max-width: 767px) {
    padding: 0.5rem 0.5rem 0.5rem 2rem;
    font-size: 0.9rem;
    border-radius: 6px;
  }

  /* Small mobile */
  @media (max-width: 480px) {
    padding: 0.5rem 0.5rem 0.5rem 1.75rem;
    font-size: 0.875rem;
  }

  /* Very small mobile */
  @media (max-width: 360px) {
    padding: 0.5rem 0.5rem 0.5rem 1.5rem;
    font-size: 0.8rem;
  }
`;

const ErrorMessage = styled.div`
  color: #ef4444;
  font-size: 0.875rem;
  margin-top: 0.25rem;
  display: flex;
  align-items: center;
  gap: 0.25rem;

  /* Mobile */
  @media (max-width: 767px) {
    font-size: 0.8rem;
  }
`;

const StyledButton = styled(Button)`
  background-color: #4f46e5 !important;
  border-radius: 8px !important;
  padding: 0.75rem !important;
  font-size: 1rem !important;
  font-weight: 500 !important;

  /* Tablet */
  @media (max-width: 1023px) {
    padding: 0.625rem !important;
    font-size: 0.95rem !important;
  }

  /* Mobile */
  @media (max-width: 767px) {
    padding: 0.625rem !important;
    font-size: 0.9rem !important;
    border-radius: 6px !important;
  }
`;

const CheckboxContainer = styled.div`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin: 1rem 0;

  /* Mobile */
  @media (max-width: 767px) {
    gap: 0.375rem;
    margin: 0.75rem 0;
  }
`;

const CheckboxWrapper = styled.div`
  position: relative;
  display: flex;
  align-items: center;
`;

const CheckboxInput = styled.input`
  width: 1rem;
  height: 1rem;
  border: 1px solid #e5e7eb;
  border-radius: 4px;
  background-color: #f9fafb;
  cursor: pointer;
  transition: all 0.2s ease;

  &:checked {
    background-color: #4f46e5;
    border-color: #4f46e5;
  }

  &:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.2);
  }

  /* Mobile */
  @media (max-width: 767px) {
    width: 0.875rem;
    height: 0.875rem;
  }
`;

const CheckboxLabel = styled.label`
  font-size: 0.875rem;
  color: #4b5563;
  cursor: pointer;
  user-select: none;

  /* Mobile */
  @media (max-width: 767px) {
    font-size: 0.8rem;
  }
`;

const SignUpLink = styled.div`
  text-align: center;
  margin-top: 1.5rem;
  font-size: 0.875rem;
  color: #4b5563;

  a {
    color: #4f46e5;
    text-decoration: none;
    font-weight: 500;

    &:hover {
      text-decoration: underline;
    }
  }

  /* Tablet */
  @media (max-width: 1023px) {
    margin-top: 1.25rem;
  }

  /* Mobile */
  @media (max-width: 767px) {
    margin-top: 1rem;
    font-size: 0.8rem;
  }
`;

export default function LoginForm({
  onSubmit,
  isLoading: propIsLoading,
  error: propError,
}: LoginFormProps) {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(propIsLoading || false);
  const [error, setError] = useState<string | null>(propError || null);

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<LoginFormData>({
    defaultValues: {
      rememberMe: false,
    },
  });

  const handleLogin = async (data: LoginFormData) => {
    if (onSubmit) {
      onSubmit(data);
      return;
    }

    try {
      setIsLoading(true);
      setError(null);

      const response = await fetch('/api/v1/login', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          email: data.email,
          password: data.password,
          rememberMe: data.rememberMe
        }),
      });

      // Check if the response is JSON
      const contentType = response.headers.get('content-type');
      if (!contentType || !contentType.includes('application/json')) {
        throw new Error(
          'API endpoint not found or not returning JSON. Please check the API configuration.'
        );
      }

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || 'Login failed. Please check your credentials.');
      }

      const result = await response.json().catch(() => ({}));

      // Set token in cookie using cookies-next
      // Use different expiration based on rememberMe preference
      const maxAge = data.rememberMe
        ? 60 * 60 * 24 * 30  // 30 days for remember me
        : 60 * 60 * 24;      // 24 hours for normal login

      setCookie('access_token', result.token, {
        path: '/',
        maxAge,
        sameSite: 'strict',
        secure: true,
      });

      // Redirect to dashboard
      router.push('/kanban');
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unexpected error occurred');
      console.error('Login error:', err);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <FormContainer>
      <FormHeader>
        <Title>เข้าสู่ระบบ</Title>
        <Subtitle>กรุณากรอกอีเมล และรหัสผ่านเพื่อเข้าสู่ระบบ</Subtitle>
      </FormHeader>
      <Form onSubmit={handleSubmit(handleLogin)}>
        <FormGroup>
          <Label htmlFor="email">อีเมล</Label>
          <InputWrapper>
            <IconWrapper>
              <Mail size={18} />
            </IconWrapper>
            <Input
              id="email"
              type="text"
              placeholder="<EMAIL>"
              {...register('email', {
                required: 'กรุณากรอกอีเมล',
                pattern: {
                  value: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
                  message: 'กรุณากรอกอีเมลที่ถูกต้อง',
                },
              })}
            />
          </InputWrapper>
          {errors.email && <ErrorMessage>{errors.email.message}</ErrorMessage>}
        </FormGroup>

        <FormGroup>
          <Label htmlFor="password">รหัสผ่าน</Label>
          <InputWrapper>
            <IconWrapper>
              <Lock size={18} />
            </IconWrapper>
            <Input
              id="password"
              type="password"
              placeholder="••••••••"
              {...register('password', {
                required: 'กรุณากรอกรหัสผ่าน',
                minLength: {
                  value: 6,
                  message: 'กรุณากรอกรหัสผ่านที่มีความยาวไม่ต่ำกว่า 6 ตัวอักษร',
                },
              })}
            />
          </InputWrapper>
          {errors.password && <ErrorMessage>{errors.password.message}</ErrorMessage>}
        </FormGroup>

        <CheckboxContainer>
          <CheckboxWrapper>
            <CheckboxInput
              id="rememberMe"
              type="checkbox"
              {...register('rememberMe')}
            />
          </CheckboxWrapper>
          <CheckboxLabel htmlFor="rememberMe">
            จดจำการเข้าสู่ระบบ
          </CheckboxLabel>
        </CheckboxContainer>

        {error && <ErrorMessage>{error}</ErrorMessage>}

        <StyledButton
          type="submit"
          $fullWidth
          disabled={isLoading}
        >
          {isLoading ? 'กำลังเข้าสู่ระบบ...' : 'เข้าสู่ระบบ'}
        </StyledButton>

        <SignUpLink>
          ยังไม่มีบัญชี? <a href="/register">สมัครสมาชิก</a>
        </SignUpLink>
      </Form>
    </FormContainer>
  );
}

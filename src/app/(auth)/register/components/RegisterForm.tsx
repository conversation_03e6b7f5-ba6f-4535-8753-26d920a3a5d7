'use client';

import React, { useState } from 'react';
import styled from 'styled-components';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/Button';
import { useForm } from 'react-hook-form';
import { Mail, Lock, User, Building, Phone } from 'lucide-react';
import Link from 'next/link';

// Types
interface RegisterFormProps {
  onSubmit?: (data: RegisterFormData) => void;
  isLoading?: boolean;
  error?: string | null;
}

interface RegisterFormData {
  email: string;
  password: string;
  confirmPassword: string;
  firstName: string;
  lastName: string;
  phone: string;
  organizationName: string;
}

// Styled Components
const FormContainer = styled.div`
  width: 100%;
`;

const FormHeader = styled.div`
  margin-bottom: 2rem;

  /* Tablet */
  @media (max-width: 1023px) {
    margin-bottom: 1.5rem;
  }

  /* Mobile */
  @media (max-width: 767px) {
    margin-bottom: 1.25rem;
  }
`;

const Title = styled.h1`
  font-size: 1.75rem;
  font-weight: 600;
  color: #111827;
  margin-bottom: 0.75rem;

  /* Tablet */
  @media (max-width: 1023px) {
    font-size: 1.5rem;
  }

  /* Mobile */
  @media (max-width: 767px) {
    font-size: 1.375rem;
    text-align: center;
  }

  /* Small mobile */
  @media (max-width: 480px) {
    font-size: 1.25rem;
  }
`;

const Subtitle = styled.p`
  color: #6b7280;
  font-size: 0.9rem;
  margin-bottom: 1.5rem;

  /* Tablet */
  @media (max-width: 1023px) {
    font-size: 0.875rem;
    margin-bottom: 1.25rem;
  }

  /* Mobile */
  @media (max-width: 767px) {
    font-size: 0.8rem;
    margin-bottom: 1rem;
    text-align: center;
  }
`;

const Form = styled.form`
  display: flex;
  flex-direction: column;
  gap: 1.5rem;

  /* Tablet */
  @media (max-width: 1023px) {
    gap: 1.25rem;
  }

  /* Mobile */
  @media (max-width: 767px) {
    gap: 1rem;
  }
`;

const FormGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: 0.5rem;

  /* Mobile */
  @media (max-width: 767px) {
    gap: 0.375rem;
  }
`;

const FormRow = styled.div`
  display: flex;
  gap: 1rem;

  > * {
    flex: 1;
  }

  /* Tablet */
  @media (max-width: 1023px) {
    gap: 0.75rem;
  }

  /* Mobile */
  @media (max-width: 767px) {
    flex-direction: column;
    gap: 1rem;
  }
`;

const Label = styled.label`
  font-size: 0.9rem;
  color: #4b5563;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 0.5rem;

  /* Mobile */
  @media (max-width: 767px) {
    font-size: 0.875rem;
    gap: 0.375rem;
  }
`;

const InputWrapper = styled.div`
  position: relative;
`;

const IconWrapper = styled.div`
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #6b7280;

  /* Tablet */
  @media (max-width: 1023px) {
    left: 10px;
  }

  /* Mobile */
  @media (max-width: 767px) {
    left: 8px;
  }

  /* Small mobile */
  @media (max-width: 480px) {
    left: 6px;
  }

  /* Very small mobile */
  @media (max-width: 360px) {
    left: 4px;
  }
`;

const Input = styled.input`
  width: 100%;
  padding: 0.75rem 0.75rem 0.75rem 2.5rem;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  font-size: 1rem;
  color: #111827;
  background-color: #f9fafb;
  transition: all 0.2s ease;

  &:focus {
    outline: none;
    border-color: #4f46e5;
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.2);
    background-color: white;
  }

  &::placeholder {
    color: #9ca3af;
  }

  /* Tablet */
  @media (max-width: 1023px) {
    padding: 0.625rem 0.625rem 0.625rem 2.25rem;
    font-size: 0.95rem;
  }

  /* Mobile */
  @media (max-width: 767px) {
    padding: 0.5rem 0.5rem 0.5rem 2rem;
    font-size: 0.9rem;
    border-radius: 6px;
  }

  /* Small mobile */
  @media (max-width: 480px) {
    padding: 0.5rem 0.5rem 0.5rem 1.75rem;
    font-size: 0.875rem;
  }

  /* Very small mobile */
  @media (max-width: 360px) {
    padding: 0.5rem 0.5rem 0.5rem 1.5rem;
    font-size: 0.8rem;
  }
`;

const ErrorMessage = styled.div`
  color: #ef4444;
  font-size: 0.875rem;
  margin-top: 0.25rem;
  display: flex;
  align-items: center;
  gap: 0.25rem;

  /* Mobile */
  @media (max-width: 767px) {
    font-size: 0.8rem;
  }
`;

const StyledButton = styled(Button)`
  background-color: #4f46e5 !important;
  border-radius: 8px !important;
  padding: 0.75rem !important;
  font-size: 1rem !important;
  font-weight: 500 !important;

  /* Tablet */
  @media (max-width: 1023px) {
    padding: 0.625rem !important;
    font-size: 0.95rem !important;
  }

  /* Mobile */
  @media (max-width: 767px) {
    padding: 0.625rem !important;
    font-size: 0.9rem !important;
    border-radius: 6px !important;
  }
`;

const LoginLink = styled.div`
  text-align: center;
  margin-top: 1.5rem;
  font-size: 0.875rem;
  color: #4b5563;

  a {
    color: #4f46e5;
    text-decoration: none;
    font-weight: 500;

    &:hover {
      text-decoration: underline;
    }
  }

  /* Tablet */
  @media (max-width: 1023px) {
    margin-top: 1.25rem;
  }

  /* Mobile */
  @media (max-width: 767px) {
    margin-top: 1rem;
    font-size: 0.8rem;
  }
`;

export default function RegisterForm({
  onSubmit,
  isLoading: propIsLoading,
  error: propError,
}: RegisterFormProps) {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(propIsLoading || false);
  const [error, setError] = useState<string | null>(propError || null);

  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
  } = useForm<RegisterFormData>();

  const password = watch('password');

  const handleRegister = async (data: RegisterFormData) => {
    if (onSubmit) {
      onSubmit(data);
      return;
    }

    try {
      setIsLoading(true);
      setError(null);

      // Check if passwords match
      if (data.password !== data.confirmPassword) {
        setError('Passwords do not match');
        setIsLoading(false);
        return;
      }

      const response = await fetch('/api/v1/register', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          email: data.email,
          password: data.password,
          firstName: data.firstName,
          lastName: data.lastName,
          phone: data.phone || '',
          organizationName: data.organizationName,
        }),
      });

      // Check if the response is JSON
      const contentType = response.headers.get('content-type');
      if (!contentType || !contentType.includes('application/json')) {
        throw new Error(
          'API endpoint not found or not returning JSON. Please check the API configuration.'
        );
      }

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || 'Registration failed. Please try again.');
      }

      const result = await response.json().catch(() => ({}));

      // Registration successful, redirect to login page
      router.push('/login?registered=true');
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unexpected error occurred');
      console.error('Registration error:', err);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <FormContainer>
      <FormHeader>
        <Title>สร้างบัญชีใหม่</Title>
        <Subtitle>กรุณากรอกรายละเอียดเพื่อสมัครสมาชิก</Subtitle>
      </FormHeader>
      <Form onSubmit={handleSubmit(handleRegister)}>
        <FormRow>
          <FormGroup>
            <Label htmlFor="firstName">ชื่อ</Label>
            <InputWrapper>
              <IconWrapper>
                <User size={18} />
              </IconWrapper>
              <Input
                id="firstName"
                type="text"
                placeholder="สมชาย"
                {...register('firstName', {
                  required: 'กรุณากรอกชื่อ',
                })}
              />
            </InputWrapper>
            {errors.firstName && <ErrorMessage>{errors.firstName.message}</ErrorMessage>}
          </FormGroup>

          <FormGroup>
            <Label htmlFor="lastName">นามสกุล</Label>
            <InputWrapper>
              <IconWrapper>
                <User size={18} />
              </IconWrapper>
              <Input
                id="lastName"
                type="text"
                placeholder="ลูกก้าบล็อก"
                {...register('lastName', {
                  required: 'กรุณากรอกนามสกุล',
                })}
              />
            </InputWrapper>
            {errors.lastName && <ErrorMessage>{errors.lastName.message}</ErrorMessage>}
          </FormGroup>
        </FormRow>

        <FormGroup>
          <Label htmlFor="email">อีเมล</Label>
          <InputWrapper>
            <IconWrapper>
              <Mail size={18} />
            </IconWrapper>
            <Input
              id="email"
              type="text"
              placeholder="<EMAIL>"
              {...register('email', {
                required: 'กรุณากรอกอีเมล',
                pattern: {
                  value: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
                  message: 'กรุณากรอกอีเมลที่ถูกต้อง',
                },
              })}
            />
          </InputWrapper>
          {errors.email && <ErrorMessage>{errors.email.message}</ErrorMessage>}
        </FormGroup>

        <FormGroup>
          <Label htmlFor="phone">หมายเลขโทรศัพท์ (ไม่จำเป็น)</Label>
          <InputWrapper>
            <IconWrapper>
              <Phone size={18} />
            </IconWrapper>
            <Input id="phone" type="text" placeholder="+****************" {...register('phone')} />
          </InputWrapper>
          {errors.phone && <ErrorMessage>{errors.phone.message}</ErrorMessage>}
        </FormGroup>

        <FormGroup>
          <Label htmlFor="organizationName">ชื่อองค์กร</Label>
          <InputWrapper>
            <IconWrapper>
              <Building size={18} />
            </IconWrapper>
            <Input
              id="organizationName"
              type="text"
              placeholder="ชื่อองค์กร"
              {...register('organizationName', {
                required: 'ชื่อองค์กร',
              })}
            />
          </InputWrapper>
          {errors.organizationName && (
            <ErrorMessage>{errors.organizationName.message}</ErrorMessage>
          )}
        </FormGroup>

        <FormGroup>
          <Label htmlFor="password">รหัสผ่าน</Label>
          <InputWrapper>
            <IconWrapper>
              <Lock size={18} />
            </IconWrapper>
            <Input
              id="password"
              type="password"
              placeholder="••••••••"
              {...register('password', {
                required: 'กรุณากรอกรหัสผ่าน',
                minLength: {
                  value: 8,
                  message: 'รหัสผ่านต้องมีอย่างน้อย 8 ตัวอักษร',
                },
                pattern: {
                  value: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/,
                  message:
                    'รหัสผ่านต้องประกอบด้วยตัวอักษรพิมพ์ใหญ่, ตัวอักษรพิมพ์เล็ก, ตัวเลข, และตัวอักษรพิเศษ',
                },
              })}
            />
          </InputWrapper>
          {errors.password && <ErrorMessage>{errors.password.message}</ErrorMessage>}
        </FormGroup>

        <FormGroup>
          <Label htmlFor="confirmPassword">ยืนยันรหัสผ่าน</Label>
          <InputWrapper>
            <IconWrapper>
              <Lock size={18} />
            </IconWrapper>
            <Input
              id="confirmPassword"
              type="password"
              placeholder="••••••••"
              {...register('confirmPassword', {
                required: 'กรุณากรอกรหัสผ่านอีกครั้ง',
                validate: value => value === password || 'รหัสผ่านไม่ตรงกัน',
              })}
            />
          </InputWrapper>
          {errors.confirmPassword && <ErrorMessage>{errors.confirmPassword.message}</ErrorMessage>}
        </FormGroup>

        {error && <ErrorMessage>{error}</ErrorMessage>}

        <StyledButton
          type="submit"
          $fullWidth
          disabled={isLoading}
        >
          {isLoading ? 'กำลังสร้างบัญชี...' : 'สร้างบัญชี'}
        </StyledButton>

        <LoginLink>
          มีบัญชีแล้ว? <Link href="/login">เข้าสู่ระบบ</Link>
        </LoginLink>
      </Form>
    </FormContainer>
  );
}

export const framework : any = [
  {
    name : "order_report",
    displayName : "การสั่งงานและรายงานผล",
    prompt : `ขยายความจากข้อความที่ส่งมาให้ละเอียดที่ครบถ้าน กระชับเเละเข้าใจง่ายมากขึ้น เเละอยู่ใน format ต่อไปนี้ ` +
      `{bold}เรียน: (กรอกชื่อ) 
       {bold}เรื่อง: (ให้ตีความจากข้อความที่ส่งมา) 
       {bold}รายละเอียด: (ขยายความจากข้อความที่ส่งมาให้ละเอียดที่ครบถ้าน กระชับเเละเข้าใจง่ายมากขึ้น)
       {bold}วันที่: (ให้ตีความจากข้อความที่ส่งมาและแปลงเป็นวันที่ {DD MMM YYYY} ถ้ามี) 
       {bold}งาน: (ให้ตีความจากข้อความที่ส่งมา) 
       {bold}กำหนดเสร็จ: (ให้ตีความจากข้อความที่ส่งมาและแปลงเป็นวันที่ {DD MMM YYYY} ถ้ามี)
       {bold}สถานะ: ☑ เสร็จแล้ว / 🔄 อยู่ระหว่างดำเนินการ / ⚠ ติดปัญหา 
       {bold}ผลลัพธ์: สรุปสิ่งที่ทำแล้วอย่างชัดเจน
       {bold}แนบไฟล์: (ถ้ามี)` +
      `โดยอันไหนที่สมควรให้กรอกเองให้ใส่เป็น {กรอกข้อมูล}`,
    style : "font-size H6 เท่ากันทั้งหมด"
  },
  {
    name : "approval",
    displayName : "การขออนุมัติ",
    prompt : `ขยายความจากข้อความที่ส่งมาให้ละเอียดที่ครบถ้าน กระชับเเละเข้าใจง่ายมากขึ้น เเละอยู่ใน format ต่อไปนี้ ` +
      `{bold}เรียน: (กรอกชื่อ) 
       {bold}เรื่อง: (ให้ตีความจากข้อความที่ส่งมา) 
       {bold}รายละเอียด: (ขยายความจากข้อความที่ส่งมาให้ละเอียดที่ครบถ้าน กระชับเเละเข้าใจง่ายมากขึ้น)
       {bold}รายการ: (ชื่อรายการ/งาน) 
       {bold}เหตุผล/ความจำเป็น: (ให้ตีความจากข้อความที่ส่งมา) 
       {bold}จำนวนเงิน: (ให้ตีความจากข้อความที่ส่งมาและอยู่ใน format {xx,xxx บาท (รวม VAT แล้ว)} ถ้ามี)
       {bold}บริษัทที่เสนอ: (ให้ตีความจากข้อความที่ส่งมา) ถ้ามี
       {bold}ระยะเวลา: (ถ้ามี)
       {bold}แนบเอกสาร: ใบเสนอราคา, รูปถ่าย (ถ้ามี)
       {bold}ความเร่งด่วน: ด่วน / ภายในวันนี้ / ปกติ` +
      `โดยอันไหนที่สมควรให้กรอกเองให้ใส่เป็น {กรอกข้อมูล}`,
    style : "font-size H6 เท่ากันทั้งหมด"
  },
  {
    name : "progress_report",
    displayName : "การรายงานผลการทำงาน",
    prompt : `ขยายความจากข้อความที่ส่งมาให้ละเอียดที่ครบถ้าน กระชับเเละเข้าใจง่ายมากขึ้น เเละอยู่ใน format ต่อไปนี้ ` +
      `{bold}เรียน: (กรอกชื่อ) 
       {bold}เรื่อง: (ให้ตีความจากข้อความที่ส่งมา) 
       {bold}รายละเอียด: (ขยายความจากข้อความที่ส่งมาให้ละเอียดที่ครบถ้าน กระชับเเละเข้าใจง่ายมากขึ้น)
       {bold}วันที่รายงาน: (ให้ตีความจากข้อความที่ส่งมาและแปลงเป็นวันที่ {DD MMM YYYY} ถ้ามี) 
       {bold}ผู้รายงาน: (ให้ตีความจากข้อความที่ส่งมา) (ชื่อ / แผนก)
       {bold}งานที่ดำเนินการ: (ให้ตีความจากข้อความที่ส่งมาเป็นข้อๆ bullet list ใน format {งานที่ 1: รายละเอียด + จำนวน})
       {bold}สรุป: (สรุปความจากข้อความที่ส่งมา) 
       {bold}ปัญหาที่พบ: (ให้ตีความจากข้อความที่ส่งมา) ถ้ามี
       {bold}สิ่งที่ต้องการความช่วยเหลือ: (ให้ตีความจากข้อความที่ส่งมา) ถ้ามี
       {bold}หมายเหตุ: (ให้ตีความจากข้อความที่ส่งมา) ถ้ามี` +
      `โดยอันไหนที่สมควรให้กรอกเองให้ใส่เป็น {กรอกข้อมูล}`,
    style : "font-size H6 เท่ากันทั้งหมด"
  },
  {
    name : "communication",
    displayName : "การสื่อสารทั่วไป / แจ้งปัญหา / เหตุฉุกเฉิน",
    prompt : `ขยายความจากข้อความที่ส่งมาให้ละเอียดที่ครบถ้าน กระชับเเละเข้าใจง่ายมากขึ้น เเละอยู่ใน format ต่อไปนี้ ` +
      `{bold}เรียน: (กรอกชื่อ) 
       {bold}เรื่อง: (ให้ตีความจากข้อความที่ส่งมา) 
       {bold}รายละเอียด: (ขยายความจากข้อความที่ส่งมาให้ละเอียดที่ครบถ้าน กระชับเเละเข้าใจง่ายมากขึ้น)
       {bold}ปัญหาคือ: (ให้ตีความจากข้อความที่ส่งมา)
       {bold}เกิดขึ้นเมื่อ: (ให้ตีความจากข้อความที่ส่งมาและแปลงเป็น format {วันที่/เวลา + สถานการณ์} ถ้ามี)  
       {bold}ส่งผลอย่างไร: (ให้ตีความจากข้อความที่ส่งมา) ถ้ามี
       {bold}ดำเนินการแล้ว: (สิ่งที่ได้ดำเนินการไปแล้ว) (ถ้ามี)
       {bold}ข้อเสนอแนะ / คำถาม: (ให้ตีความจากข้อความที่ส่งมาที่เป็น ถามเพื่อหาทางออก / ขออนุมัติแนวทาง) ถ้ามี
       {bold}แนบไฟล์: ไฟล์หรือภาพประกอบ (ถ้ามี)` +
      `โดยอันไหนที่สมควรให้กรอกเองให้ใส่เป็น {กรอกข้อมูล}`,
    style : "font-size H6 เท่ากันทั้งหมด"
  }
]

export const ai_models : any = [
  // OpenAI
  {
    name: "openai/gpt-5",
    displayName : "GPT-5",
    price_input : 1.25,
    price_output : 10,
  },
  {
    name: "openai/gpt-5-mini",
    displayName : "GPT-5 Mini",
    price_input : 0.25,
    price_output : 2,
  },
  {
    name: "openai/gpt-5-nano",
    displayName : "GPT-5 Nano",
    price_input : 0.05,
    price_output : 0.4,
  },
  {
    name: "openai/gpt-5-chat-latest",
    displayName : "GPT-5 Chat",
    price_input : 1.25,
    price_output : 10,
  },
  {
    name: "openai/gpt-4o",
    displayName : "GPT-4o",
    price_input : 2.5,
    price_output : 10,
  },
  {
    name: "openai/gpt-4.1",
    displayName : "GPT-4.1",
    price_input : 2,
    price_output : 8,
  },
  // GROK
  {
    name: "xai/grok-4-latest",
    displayName : "GROK-4",
    price_input : 3,
    price_output : 15,
  },
  {
    name: "xai/grok-3",
    displayName : "GROK-3",
    price_input : 3,
    price_output : 15,
  },
  {
    name: "xai/grok-3-mini",
    displayName : "GROK-3 Mini",
    price_input : 0.3,
    price_output : 0.5,
  },
  // Anthropic
  {
    name: "anthropic/claude-4-sonnet-20250514",
    displayName : "Claude-4 Sonnet",
    price_input : 3,
    price_output : 15,
  },
  {
    name: "anthropic/claude-3-7-sonnet-latest",
    displayName : "Claude-3.7 Sonnet",
    price_input : 3,
    price_output : 15,
  },
  {
    name: "anthropic/claude-3-5-haiku-latest",
    displayName : "Claude-3.5 Haiku",
    price_input : 0.8,
    price_output : 4,
  }
  ]
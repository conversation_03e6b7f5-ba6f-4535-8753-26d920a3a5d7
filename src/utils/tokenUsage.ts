import { ai_models } from './ai-assistant';

export interface TokenUsageData {
  promptTokens?: number;
  completionTokens?: number;
  totalTokens?: number;
}

export interface CostCalculation {
  inputCost: number;
  outputCost: number;
  totalCost: number;
}

export interface AIModelPricing {
  name: string;
  displayName: string;
  price_input: number;  // Price per 1M input tokens in USD
  price_output: number; // Price per 1M output tokens in USD
}

/**
 * Find AI model pricing information by model name
 * @param modelName - The model name (e.g., "openai/gpt-4o")
 * @returns AI model pricing information or null if not found
 */
export function findModelPricing(modelName: string): AIModelPricing | null {
  const model = ai_models.find((m: any) => m.name === modelName);
  return model || null;
}

/**
 * Calculate cost based on token usage and model pricing
 * @param tokenUsage - Token usage data
 * @param modelPricing - AI model pricing information
 * @returns Cost calculation or null if insufficient data
 */
export function calculateTokenCost(
  tokenUsage: TokenUsageData,
  modelPricing: AIModelPricing
): CostCalculation | null {
  const { promptTokens, completionTokens } = tokenUsage;
  const { price_input, price_output } = modelPricing;

  if (!promptTokens || !completionTokens || !price_input || !price_output) {
    return null;
  }

  // Prices are per 1M tokens, so divide by 1,000,000
  const inputCost = (promptTokens / 1_000_000) * price_input;
  const outputCost = (completionTokens / 1_000_000) * price_output;
  const totalCost = inputCost + outputCost;

  return {
    inputCost,
    outputCost,
    totalCost
  };
}

/**
 * Calculate cost directly from token usage and model name
 * @param tokenUsage - Token usage data
 * @param modelName - The model name
 * @returns Cost calculation or null if model not found or insufficient data
 */
export function calculateCostByModelName(
  tokenUsage: TokenUsageData,
  modelName: string
): CostCalculation | null {
  const modelPricing = findModelPricing(modelName);
  if (!modelPricing) {
    return null;
  }
  return calculateTokenCost(tokenUsage, modelPricing);
}

/**
 * Format cost amount for display
 * @param amount - Cost amount in USD
 * @returns Formatted cost string
 */
export function formatCost(amount: number): string {
  if (amount < 0.001) {
    return '<$0.001';
  }
  if (amount < 0.01) {
    return `$${amount.toFixed(4)}`;
  }
  if (amount < 1) {
    return `$${amount.toFixed(3)}`;
  }
  return `$${amount.toFixed(2)}`;
}

/**
 * Format token count for display with thousands separators
 * @param count - Token count
 * @returns Formatted token count string
 */
export function formatTokenCount(count: number): string {
  return count.toLocaleString();
}

/**
 * Get token usage summary for display
 * @param tokenUsage - Token usage data
 * @param modelName - Optional model name for cost calculation
 * @returns Summary object with formatted strings
 */
export function getTokenUsageSummary(
  tokenUsage: TokenUsageData,
  modelName?: string
) {
  const { promptTokens, completionTokens, totalTokens } = tokenUsage;
  
  const summary = {
    promptTokens: promptTokens ? formatTokenCount(promptTokens) : undefined,
    completionTokens: completionTokens ? formatTokenCount(completionTokens) : undefined,
    totalTokens: totalTokens ? formatTokenCount(totalTokens) : undefined,
    cost: null as string | null,
    modelDisplayName: null as string | null
  };

  if (modelName) {
    const modelPricing = findModelPricing(modelName);
    if (modelPricing) {
      summary.modelDisplayName = modelPricing.displayName;
      
      const cost = calculateTokenCost(tokenUsage, modelPricing);
      if (cost) {
        summary.cost = formatCost(cost.totalCost);
      }
    }
  }

  return summary;
}

/**
 * Extract model name from message metadata
 * @param metadata - Message metadata object
 * @returns Model name or null if not found
 */
export function extractModelNameFromMetadata(metadata: any): string | null {
  if (!metadata) return null;
  
  // Check common metadata fields where model name might be stored
  if (metadata.model) return metadata.model;
  if (metadata.modelName) return metadata.modelName;
  if (metadata.ai_model) return metadata.ai_model;
  
  return null;
}

/**
 * Date utility functions for consistent timestamp formatting across the application
 */

/**
 * Smart timestamp formatting that shows contextually appropriate date/time information
 * based on how recent the timestamp is (Thai language)
 *
 * @param timeString - ISO timestamp string
 * @returns Formatted timestamp string in Thai
 */
export const formatSmartTimestamp = (timeString: string): string => {
  const date = new Date(timeString);
  const now = new Date();

  // Get start of today, this week, this month, and this year
  const startOfToday = new Date(now.getFullYear(), now.getMonth(), now.getDate());
  const startOfWeek = new Date(now);
  startOfWeek.setDate(now.getDate() - now.getDay()); // Start of week (Sunday)
  startOfWeek.setHours(0, 0, 0, 0);
  const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
  const startOfYear = new Date(now.getFullYear(), 0, 1);

  // Time formatting options for Thai locale
  const timeOptions: Intl.DateTimeFormatOptions = {
    hour: '2-digit',
    minute: '2-digit',
    hour12: false,
  };

  // Check if message is from today
  if (date >= startOfToday) {
    return date.toLocaleTimeString('th-TH', timeOptions);
  }

  // Check if message is from this week
  if (date >= startOfWeek) {
    const dayName = date.toLocaleDateString('th-TH', { weekday: 'short' });
    const time = date.toLocaleTimeString('th-TH', timeOptions);
    return `${dayName} ${time}`;
  }

  // Check if message is from this month
  if (date >= startOfMonth) {
    const monthDay = date.toLocaleDateString('th-TH', {
      month: 'short',
      day: 'numeric'
    });
    const time = date.toLocaleTimeString('th-TH', timeOptions);
    return `${monthDay} ${time}`;
  }

  // Check if message is from this year
  if (date >= startOfYear) {
    const monthDay = date.toLocaleDateString('th-TH', {
      month: 'short',
      day: 'numeric'
    });
    const time = date.toLocaleTimeString('th-TH', timeOptions);
    return `${monthDay} ${time}`;
  }

  // Message is from previous years
  const fullDate = date.toLocaleDateString('th-TH', {
    month: 'short',
    day: 'numeric',
    year: 'numeric'
  });
  const time = date.toLocaleTimeString('th-TH', timeOptions);
  return `${fullDate} ${time}`;
};

/**
 * Simple timestamp formatting for sidebar/list views (Thai language)
 * Shows time for today, date for older messages
 *
 * @param timeString - ISO timestamp string
 * @returns Formatted timestamp string in Thai
 */
export const formatSimpleTimestamp = (timeString?: string): string => {
  if (!timeString) return '';

  const date = new Date(timeString);
  const now = new Date();
  const diff = now.getTime() - date.getTime();
  const hours = Math.floor(diff / (1000 * 60 * 60));

  if (hours < 24) {
    return date.toLocaleTimeString('th-TH', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: false,
    });
  } else {
    return date.toLocaleDateString('th-TH', {
      month: 'short',
      day: 'numeric',
    });
  }
};

/**
 * Format timestamp for read status tooltips (Thai language)
 * Shows relative time (e.g., "5 นาทีที่แล้ว", "2 ชั่วโมงที่แล้ว", "เมื่อสักครู่")
 *
 * @param readAt - ISO timestamp string
 * @returns Formatted relative time string in Thai
 */
export const formatReadTime = (readAt: string): string => {
  const date = new Date(readAt);
  const now = new Date();
  const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));

  if (diffInMinutes < 1) return 'เมื่อสักครู่';
  if (diffInMinutes < 60) return `${diffInMinutes} นาทีที่แล้ว`;
  if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)} ชั่วโมงที่แล้ว`;
  return date.toLocaleDateString('th-TH');
};

/**
 * Format full date and time for detailed views (Thai language)
 *
 * @param timestamp - ISO timestamp string
 * @returns Formatted full date and time string in Thai
 */
export const formatFullDateTime = (timestamp: string): string => {
  try {
    const date = new Date(timestamp);
    return date.toLocaleString('th-TH');
  } catch {
    return timestamp;
  }
};

import DOMPurify from 'dompurify';

/**
 * Configuration for sanitizing AI Assistant HTML content
 * Allows common formatting elements while preventing XSS attacks
 */
const AI_ASSISTANT_SANITIZE_CONFIG = {
  // Allowed HTML tags for AI Assistant messages
  ALLOWED_TAGS: [
    'p', 'br', 'div', 'span',           // Basic structure
    'strong', 'b', 'em', 'i', 'u',     // Text formatting
    'h1', 'h2', 'h3', 'h4', 'h5', 'h6', // Headings
    'ul', 'ol', 'li',                   // Lists
    'a',                                // Links
    'code', 'pre',                      // Code formatting
    'blockquote',                       // Quotes
    'hr',                               // Horizontal rule
  ],
  
  // Allowed attributes
  ALLOWED_ATTR: [
    'href', 'target', 'rel',            // Link attributes
    'class',                            // CSS classes
    'data-*',                           // Data attributes (for mentions, etc.)
  ],
  
  // Additional security settings
  ALLOW_DATA_ATTR: true,
  ALLOW_UNKNOWN_PROTOCOLS: false,
  SANITIZE_DOM: true,
  KEEP_CONTENT: true,
  
  // Transform functions for specific elements
  FORBID_ATTR: ['style', 'onclick', 'onload', 'onerror'], // Block inline styles and event handlers
  FORBID_TAGS: ['script', 'iframe', 'object', 'embed', 'form', 'input'], // Block dangerous tags
};

/**
 * Sanitizes HTML content for AI Assistant messages
 * Removes potentially dangerous elements while preserving formatting
 * 
 * @param htmlContent - Raw HTML content from AI Assistant
 * @returns Sanitized HTML content safe for rendering
 */
export function sanitizeAIAssistantHTML(htmlContent: string): string {
  if (!htmlContent || typeof htmlContent !== 'string') {
    return '';
  }

  // Add hook to sanitize href attributes
  DOMPurify.addHook('afterSanitizeAttributes', function (node) {
    // Set all links to open in new tab and add security attributes
    if (node.tagName === 'A') {
      const href = node.getAttribute('href');
      if (href) {
        // Only allow safe protocols
        if (href.match(/^(https?:|mailto:)/i)) {
          node.setAttribute('target', '_blank');
          node.setAttribute('rel', 'noopener noreferrer');
        } else {
          // Remove unsafe links
          node.removeAttribute('href');
        }
      }
    }
  });

  // Configure DOMPurify with our AI Assistant specific settings
  const sanitized = DOMPurify.sanitize(htmlContent, AI_ASSISTANT_SANITIZE_CONFIG);

  // Remove the hook after use to avoid affecting other sanitizations
  DOMPurify.removeAllHooks();

  return sanitized;
}

/**
 * Processes AI Assistant message content for safe HTML rendering
 * Handles both plain text and HTML content
 * 
 * @param content - Raw content from AI Assistant (could be plain text or HTML)
 * @returns Sanitized HTML content ready for dangerouslySetInnerHTML
 */
export function processAIAssistantContent(content: string): string {
  if (!content || typeof content !== 'string') {
    return '';
  }

  // If content doesn't contain HTML tags, treat as plain text and convert line breaks
  if (!content.includes('<')) {
    // Convert line breaks to <br> tags and wrap in paragraph
    const withBreaks = content
      .replace(/\n\n+/g, '</p><p>') // Double line breaks become paragraph breaks
      .replace(/\n/g, '<br>');      // Single line breaks become <br> tags
    
    return `<p>${withBreaks}</p>`;
  }

  // Content contains HTML, sanitize it
  return sanitizeAIAssistantHTML(content);
}

/**
 * Checks if content contains HTML tags
 * 
 * @param content - Content to check
 * @returns True if content contains HTML tags
 */
export function containsHTML(content: string): boolean {
  return typeof content === 'string' && content.includes('<');
}

/**
 * Strips HTML tags from content and returns plain text
 * Useful for previews or fallback display
 * 
 * @param htmlContent - HTML content to strip
 * @returns Plain text content
 */
export function stripHTML(htmlContent: string): string {
  if (!htmlContent || typeof htmlContent !== 'string') {
    return '';
  }

  // Use DOMPurify to strip all HTML tags
  return DOMPurify.sanitize(htmlContent, { 
    ALLOWED_TAGS: [],
    KEEP_CONTENT: true 
  });
}

/**
 * Configuration for different content types
 * Can be extended for other use cases beyond AI Assistant
 */
export const SANITIZE_CONFIGS = {
  AI_ASSISTANT: AI_ASSISTANT_SANITIZE_CONFIG,
  
  // More restrictive config for user-generated content
  USER_CONTENT: {
    ALLOWED_TAGS: ['p', 'br', 'strong', 'em', 'u'],
    ALLOWED_ATTR: ['class'],
    FORBID_ATTR: ['style', 'onclick', 'onload', 'onerror'],
    FORBID_TAGS: ['script', 'iframe', 'object', 'embed', 'form', 'input'],
  },
  
  // Very restrictive config for previews
  PREVIEW: {
    ALLOWED_TAGS: ['strong', 'em'],
    ALLOWED_ATTR: [],
    FORBID_ATTR: ['style', 'onclick', 'onload', 'onerror'],
    FORBID_TAGS: ['script', 'iframe', 'object', 'embed', 'form', 'input'],
  }
} as const;

export interface MentionUser {
  id: number;
  firstName: string;
  lastName: string;
  imageUrl?: string;
}

export interface MentionData {
  id: string;
  userId: number;
  displayName: string;
  type: 'user';
}

export interface MentionMatch {
  text: string;
  index: number;
  length: number;
  query: string;
}

export interface MentionPopoverPosition {
  top: number;
  left: number;
}

export interface MentionPopoverProps {
  isVisible: boolean;
  position: MentionPopoverPosition;
  users: MentionUser[];
  query: string;
  selectedIndex: number;
  onUserSelect: (user: MentionUser) => void;
  onClose: () => void;
}

export interface MentionHookResult {
  showMentionPopover: boolean;
  mentionPosition: MentionPopoverPosition;
  mentionQuery: string;
  filteredUsers: MentionUser[];
  selectedMentionIndex: number;
  handleMentionKeyDown: (e: React.KeyboardEvent) => boolean;
  handleMentionSelect: (user: MentionUser) => void;
  closeMentionPopover: () => void;
}

// Utility functions for mention formatting
export const MENTION_REGEX = /@\[([^\]]+)\]\((\d+)\)/g;
export const MENTION_TRIGGER = '@';

export const formatMentionForDisplay = (displayName: string): string => {
  return `@${displayName}`;
};

export const formatMentionForStorage = (displayName: string, userId: number): string => {
  return `@[${displayName}](${userId})`;
};

export const parseMentionsFromText = (text: string): MentionData[] => {
  const mentions: MentionData[] = [];
  let match;
  
  while ((match = MENTION_REGEX.exec(text)) !== null) {
    mentions.push({
      id: `mention-${match[2]}-${Date.now()}`,
      userId: parseInt(match[2]),
      displayName: match[1],
      type: 'user'
    });
  }
  
  return mentions;
};

export const replaceMentionsWithHTML = (text: string): string => {
  return text.replace(MENTION_REGEX, (match, displayName, userId) => {
    return `<span class="mention" data-user-id="${userId}" data-mention="true">@${displayName}</span>`;
  });
};

export const extractTextFromHTML = (html: string): string => {
  // Convert mention spans back to mention format for storage
  const tempDiv = document.createElement('div');
  tempDiv.innerHTML = html;
  
  const mentionSpans = tempDiv.querySelectorAll('.mention[data-mention="true"]');
  mentionSpans.forEach(span => {
    const userId = span.getAttribute('data-user-id');
    const displayName = span.textContent?.replace('@', '') || '';
    if (userId && displayName) {
      span.outerHTML = formatMentionForStorage(displayName, parseInt(userId));
    }
  });
  
  return tempDiv.textContent || tempDiv.innerText || '';
};

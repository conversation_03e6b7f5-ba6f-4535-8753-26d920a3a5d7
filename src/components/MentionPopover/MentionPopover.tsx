'use client';

import React, { useEffect, useRef } from 'react';
import styled from 'styled-components';
import { appTheme } from '@/app/theme';
import { MentionPopoverProps, MentionUser } from '@/types/mention';

const PopoverContainer = styled.div<{ $top: number; $left: number; $visible: boolean }>`
  position: absolute;
  top: ${props => props.$top}px;
  left: ${props => props.$left}px;
  background: ${appTheme.colors.background.main};
  border: 1px solid ${appTheme.colors.border};
  border-radius: ${appTheme.borderRadius.sm};
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.12);
  max-height: 120px;
  min-width: 160px;
  max-width: 220px;
  overflow-y: auto;
  z-index: 1000;
  opacity: ${props => props.$visible ? 1 : 0};
  visibility: ${props => props.$visible ? 'visible' : 'hidden'};
  transform: ${props => props.$visible ? 'translateY(0)' : 'translateY(-10px)'};
  transition: all 0.2s ease;

  /* Scrollbar styling */
  &::-webkit-scrollbar {
    width: 3px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background: ${appTheme.colors.border};
    border-radius: 2px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: ${appTheme.colors.text.light};
  }
`;

const UserItem = styled.div<{ $selected: boolean }>`
  display: flex;
  align-items: center;
  padding: ${appTheme.spacing.xs} ${appTheme.spacing.sm};
  cursor: pointer;
  background: ${props => props.$selected ? appTheme.colors.background.lighter : 'transparent'};
  border-bottom: 1px solid ${appTheme.colors.border};
  transition: background-color 0.15s ease;
  min-height: 36px;

  &:hover {
    background: ${appTheme.colors.background.lighter};
  }

  &:last-child {
    border-bottom: none;
  }
`;

const UserAvatar = styled.div<{ $imageUrl?: string }>`
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: ${props => props.$imageUrl ? `url(${props.$imageUrl})` : appTheme.colors.primary};
  background-size: cover;
  background-position: center;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 10px;
  font-weight: 600;
  margin-right: ${appTheme.spacing.xs};
  flex-shrink: 0;
`;

const UserInfo = styled.div`
  flex: 1;
  min-width: 0;
`;

const UserName = styled.div`
  font-size: 12px;
  font-weight: 500;
  color: ${appTheme.colors.text.primary};
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
`;

const NoResults = styled.div`
  padding: ${appTheme.spacing.sm};
  text-align: center;
  color: ${appTheme.colors.text.secondary};
  font-size: 12px;
`;

const shouldShowUserImage = (imageUrl?: string | null): boolean => {
  return !!(imageUrl && imageUrl.trim() && imageUrl !== 'null' && imageUrl !== 'undefined');
};

const getUserInitials = (firstName?: string, lastName?: string): string => {
  const first = firstName?.charAt(0)?.toUpperCase() || '';
  const last = lastName?.charAt(0)?.toUpperCase() || '';
  return first + last || '?';
};

export default function MentionPopover({
  isVisible,
  position,
  users,
  query,
  selectedIndex,
  onUserSelect,
  onClose
}: MentionPopoverProps) {
  const popoverRef = useRef<HTMLDivElement>(null);
  const selectedItemRef = useRef<HTMLDivElement>(null);

  // Scroll selected item into view
  useEffect(() => {
    if (selectedItemRef.current && isVisible) {
      selectedItemRef.current.scrollIntoView({
        block: 'nearest',
        behavior: 'smooth'
      });
    }
  }, [selectedIndex, isVisible]);

  // Handle click outside to close
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (popoverRef.current && !popoverRef.current.contains(event.target as Node)) {
        onClose();
      }
    };

    if (isVisible) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => document.removeEventListener('mousedown', handleClickOutside);
    }
  }, [isVisible, onClose]);

  if (!isVisible) {
    return null;
  }

  return (
    <PopoverContainer
      ref={popoverRef}
      $top={position.top}
      $left={position.left}
      $visible={isVisible}
    >
      {users.length === 0 ? (
        <NoResults>
          {query ? `No users found for "${query}"` : 'No users available'}
        </NoResults>
      ) : (
        users.map((user, index) => (
          <UserItem
            key={user.id}
            ref={index === selectedIndex ? selectedItemRef : undefined}
            $selected={index === selectedIndex}
            onClick={() => onUserSelect(user)}
            onMouseEnter={() => {
              // Optional: Update selected index on hover
              // This can be implemented if desired
            }}
          >
            <UserAvatar
              $imageUrl={shouldShowUserImage(user.imageUrl) ? user.imageUrl || undefined : undefined}
              title={`${user.firstName} ${user.lastName}`}
            >
              {!shouldShowUserImage(user.imageUrl) &&
                getUserInitials(user.firstName, user.lastName)}
            </UserAvatar>
            <UserInfo>
              <UserName>
                {user.firstName} {user.lastName}
              </UserName>
            </UserInfo>
          </UserItem>
        ))
      )}
    </PopoverContainer>
  );
}

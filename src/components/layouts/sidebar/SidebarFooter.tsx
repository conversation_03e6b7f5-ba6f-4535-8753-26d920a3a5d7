'use client';

import React from 'react';
import styled from 'styled-components';
import { LogOut } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { deleteCookie } from 'cookies-next';
import { Tooltip } from '@/components/ui/tooltip';
import { useSidebar } from '../Sidebar';

const FooterContainer = styled.div<{ $isCollapsed: boolean }>`
  padding: ${props => (props.$isCollapsed ? '0.75rem' : '0.75rem')};
  display: flex;
  align-items: center;
  margin-top: auto;
  justify-content: ${props => (props.$isCollapsed ? 'center' : 'flex-start')};
`;

const LogoutButton = styled.button<{ $isCollapsed: boolean }>`
  display: flex;
  align-items: center;
  background-color: #f3f4f6;
  border: 1px solid rgba(0, 0, 0, 0.1);
  color: #6b7280;
  cursor: pointer;
  padding: ${props => (props.$isCollapsed ? '0.875rem' : '0.875rem 1rem')};
  border-radius: 0.75rem;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  width: ${props => (props.$isCollapsed ? '3rem' : '100%')};
  height: 3rem;
  justify-content: ${props => (props.$isCollapsed ? 'center' : 'flex-start')};
  font-weight: 500;
  font-size: 0.875rem;
  line-height: 1.25rem;

  &:hover {
    background-color: #ef4444;
    color: white;
    border-color: #ef4444;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(239, 68, 68, 0.2);
  }

  &:focus {
    outline: none;
  }

  &:focus-visible {
    outline: none;
  }

  &:active {
    transform: translateY(0);
  }

  svg {
    stroke-width: 1.5px;
    margin-right: ${props => (props.$isCollapsed ? '0' : '0.75rem')};
    width: 1.25rem;
    height: 1.25rem;
  }
`;

const LogoutText = styled.span<{ $isCollapsed: boolean }>`
  display: ${props => (props.$isCollapsed ? 'none' : 'block')};
  white-space: nowrap;
`;

export default function SidebarFooter() {
  const router = useRouter();
  const { isCollapsed } = useSidebar();

  // Handle logout
  const handleLogout = () => {
    // Clear the token using cookies-next
    deleteCookie('access_token');
    // Redirect to login
    router.push('/login');
  };

  const footerContent = (
    <FooterContainer $isCollapsed={isCollapsed}>
      <LogoutButton $isCollapsed={isCollapsed} onClick={handleLogout} aria-label="Logout">
        <LogOut size={20} />
        <LogoutText $isCollapsed={isCollapsed}>ออกจากระบบ</LogoutText>
      </LogoutButton>
    </FooterContainer>
  );

  // Show tooltip only when collapsed
  if (isCollapsed) {
    return (
      <Tooltip content="ออกจากระบบ" position="right">
        {footerContent}
      </Tooltip>
    );
  }

  return footerContent;
}
